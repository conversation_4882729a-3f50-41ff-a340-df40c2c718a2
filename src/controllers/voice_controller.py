"""
API接口层 - 语音交互控制器
职责：接收HTTP请求，验证输入，调用服务层，返回响应
"""
import time
from fastapi import APIRouter, Request, HTTPException, status
from fastapi.responses import JSONResponse

from src.logger import get_logger
from src.models import (
    VoiceInteractRequest, 
    VoiceInteractResponse, 
    ErrorResponse
)
# from src.services.voice_interaction_service import VoiceInteractionService

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1", tags=["voice"])


@router.post("/voice/interact", response_model=VoiceInteractResponse)
async def voice_interact(request: Request, voice_request: VoiceInteractRequest):
    """
    语音交互接口
    
    这是语音交互流程的核心接口，接收音频数据和会话信息，
    返回完整的交互结果包括文本回复、UI组件和设备状态更新。
    """
    start_time = time.time()
    request_id = voice_request.request_id
    
    logger.info(
        "Voice interaction request received",
        request_id=request_id,
        building_id=voice_request.extend.building_id,
        audio_format=voice_request.audio_format.model_dump()
    )
    
    try:
        # 调用VoiceInteractionService处理业务逻辑
        from src.services import VoiceInteractionService

        voice_service = VoiceInteractionService()
        try:
            response = await voice_service.process_voice_interaction(voice_request)
            return response
        finally:
            # 确保连接器正确关闭
            await voice_service.close()

        
    except Exception as e:
        cost = int((time.time() - start_time) * 1000)
        
        logger.error(
            "Voice interaction failed",
            request_id=request_id,
            error=str(e),
            cost=cost
        )
        
        error_response = ErrorResponse(
            requestId=request_id,
            timestamp=int(time.time() * 1000),
            code="PROCESSING_ERROR",
            msg=f"语音交互处理失败: {str(e)}"
        )
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error_response.model_dump(by_alias=True)
        )
