"""
数据模型定义
基于技术方案文档中的API契约定义请求和响应模型
"""
from typing import Optional, List, Dict, Any, Union
from pydantic import BaseModel, Field
from enum import Enum


class AudioFormat(BaseModel):
    """音频格式"""
    sample_rate: int = Field(alias="sampleRate", description="采样率，固定16000")
    channels: int = Field(description="声道数，固定1")
    encoding: str = Field(description="编码格式，固定PCM_16BIT")

    class Config:
        populate_by_name = True


class ExtendParams(BaseModel):
    """扩展参数"""
    authorization: str = Field(description="用户屏端登录会话信息")
    building_id: str = Field(alias="buildingId", description="楼宇空间ID")
    
    class Config:
        populate_by_name = True


class VoiceInteractRequest(BaseModel):
    """语音交互请求"""
    request_id: str = Field(alias="requestId", description="唯一请求标识，UUID格式")
    token: str = Field(description="授权访问服务端加密参数")
    timestamp: int = Field(description="请求时间戳（毫秒）")
    audio_data: str = Field(alias="audioData", description="Base64编码的音频数据")
    audio_format: AudioFormat = Field(alias="audioFormat", description="音频格式信息")
    extend: ExtendParams = Field(description="扩展参数")

    class Config:
        populate_by_name = True


class InteractionType(str, Enum):
    """交互类型"""
    RESULT = "result"
    CONFIRMATION = "confirmation"
    CLARIFICATION = "clarification"
    QUERY_RESULT = "query_result"
    ERROR = "error"
    CONVERSATION = "conversation"


class UIAction(BaseModel):
    """UI操作按钮"""
    type: str = Field(description="按钮类型")
    text: str = Field(description="按钮文本")
    value: str = Field(description="按钮值")
    style: Optional[str] = Field(default=None, description="按钮样式")


class UIComponent(BaseModel):
    """UI组件"""
    type: str = Field(description="UI组件类型")
    title: Optional[str] = Field(default=None, description="标题文本")
    content: Optional[str] = Field(default=None, description="主要内容")
    status: Optional[str] = Field(default=None, description="状态")
    actions: Optional[List[UIAction]] = Field(default=None, description="操作按钮列表")
    data: Optional[Dict[str, Any]] = Field(default=None, description="组件特定数据")


class DeviceUpdate(BaseModel):
    """设备状态更新"""
    device_id: str = Field(alias="deviceId", description="设备ID")
    device_name: Optional[str] = Field(alias="deviceName", default=None, description="设备名称")
    properties: Dict[str, Any] = Field(description="设备属性")
    timestamp: Optional[int] = Field(default=None, description="更新时间戳")
    
    class Config:
        populate_by_name = True


class ContextData(BaseModel):
    """上下文数据"""
    session_id: str = Field(alias="sessionId", description="会话ID")
    conversation_id: Optional[str] = Field(alias="conversationId", default=None, description="对话ID")
    last_operation: Optional[Dict[str, Any]] = Field(alias="lastOperation", default=None, description="最后操作")
    pending_confirmation: Optional[Dict[str, Any]] = Field(alias="pendingConfirmation", default=None, description="待确认操作")
    
    class Config:
        populate_by_name = True


class VoiceInteractResponseData(BaseModel):
    """语音交互响应数据"""
    text: Optional[str] = Field(default=None, description="ASR识别的用户语音文本内容")
    response_text: str = Field(alias="responseText", description="系统生成的回复文本")
    audio_url: Optional[str] = Field(alias="audioUrl", default=None, description="TTS生成的音频文件URL地址")
    audio_base64: Optional[str] = Field(alias="audioBase64", default=None, description="Base64编码的TTS音频数据")
    interaction_type: InteractionType = Field(alias="interactionType", description="交互类型标识")
    ui: Optional[UIComponent] = Field(default=None, description="UI组件")
    device_updates: Optional[List[DeviceUpdate]] = Field(alias="deviceUpdates", default=None, description="设备状态更新数据")
    context_data: Optional[ContextData] = Field(alias="contextData", default=None, description="上下文数据")
    
    class Config:
        populate_by_name = True


class VoiceInteractResponse(BaseModel):
    """语音交互响应"""
    request_id: str = Field(alias="requestId", description="对应的请求ID")
    success: bool = Field(description="处理是否成功")
    timestamp: int = Field(description="响应时间戳（毫秒）")
    cost: Optional[int] = Field(default=None, description="服务端处理时间（毫秒）")
    data: Optional[VoiceInteractResponseData] = Field(default=None, description="响应数据")
    code: str = Field(description="响应码，成功时为'0'")
    msg: str = Field(description="响应消息")
    
    class Config:
        populate_by_name = True


class ErrorResponse(BaseModel):
    """错误响应"""
    request_id: str = Field(alias="requestId", description="对应的请求ID")
    success: bool = Field(default=False, description="固定为false")
    timestamp: int = Field(description="响应时间戳（毫秒）")
    data: Dict[str, Any] = Field(default_factory=dict, description="空对象")
    code: str = Field(description="错误代码")
    msg: str = Field(description="错误描述")
    
    class Config:
        populate_by_name = True
