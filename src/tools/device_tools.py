"""
设备控制工具实现
包含设备控制和查询相关的Function Calling工具
"""
from typing import Dict, Any, Optional, List
import asyncio

from .base_tool import BaseTool, ToolExecutionResult, ToolExecutionStatus
from src.connectors import PlatformServiceConnector
from src.models import ExtendParams


class DeviceControlTool(BaseTool):
    """
    设备控制工具
    支持开关、调节亮度、温度等设备控制操作
    """
    
    def __init__(self, platform_connector: Optional[PlatformServiceConnector] = None):
        super().__init__(
            name="controlDevice",
            description="控制智能设备，如开关灯、调节亮度、设置温度等"
        )
        self.platform_connector = platform_connector or PlatformServiceConnector()
    
    def get_tool_definition(self) -> Dict[str, Any]:
        """获取设备控制工具的Function Calling定义"""
        return {
            "type": "function",
            "function": {
                "name": "controlDevice",
                "description": "控制智能设备，支持开关、调节亮度、温度等操作",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "deviceId": {
                            "type": "string",
                            "description": "设备ID，如 'light-01', 'ac-02' 等"
                        },
                        "action": {
                            "type": "string",
                            "description": "控制动作",
                            "enum": [
                                "turnOn", "turnOff", "toggle",
                                "setBrightness", "setTemperature", 
                                "setColor", "setMode"
                            ]
                        },
                        "value": {
                            "type": ["string", "number", "null"],
                            "description": "控制参数值，如亮度值(0-100)、温度值(16-30)、颜色值等"
                        },
                        "deviceName": {
                            "type": "string",
                            "description": "设备名称，用于用户友好的反馈"
                        }
                    },
                    "required": ["deviceId", "action"]
                }
            }
        }
    
    def _get_required_parameters(self) -> List[str]:
        """获取必需参数"""
        return ["deviceId", "action"]
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """验证设备控制参数"""
        # 调用基类验证
        is_valid, error_msg = super().validate_parameters(parameters)
        if not is_valid:
            return is_valid, error_msg
        
        device_id = parameters.get("deviceId")
        action = parameters.get("action")
        value = parameters.get("value")
        
        # 验证设备ID格式
        if not device_id or not isinstance(device_id, str):
            return False, "deviceId must be a non-empty string"
        
        # 验证动作类型
        valid_actions = [
            "turnOn", "turnOff", "toggle",
            "setBrightness", "setTemperature", 
            "setColor", "setMode"
        ]
        if action not in valid_actions:
            return False, f"Invalid action. Must be one of: {', '.join(valid_actions)}"
        
        # 验证需要数值参数的动作
        value_required_actions = ["setBrightness", "setTemperature"]
        if action in value_required_actions:
            if value is None:
                return False, f"Action '{action}' requires a value parameter"
            
            if action == "setBrightness":
                if not isinstance(value, (int, float)) or not (0 <= value <= 100):
                    return False, "Brightness value must be a number between 0 and 100"
            
            elif action == "setTemperature":
                if not isinstance(value, (int, float)) or not (16 <= value <= 30):
                    return False, "Temperature value must be a number between 16 and 30"
        
        return True, None
    
    async def execute(
        self, 
        call_id: str,
        parameters: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> ToolExecutionResult:
        """执行设备控制操作"""
        
        device_id = parameters["deviceId"]
        action = parameters["action"]
        value = parameters.get("value")
        device_name = parameters.get("deviceName", device_id)
        
        try:
            # 构建扩展参数
            extend_params = None
            if context:
                extend_params = ExtendParams(
                    authorization=context.get("authorization", ""),
                    buildingId=context.get("buildingId", "")
                )
            
            # 调用平台服务执行控制
            result = await self.platform_connector.control_device(
                device_id=device_id,
                action=action,
                value=value,
                extend_params=extend_params,
                request_id=call_id
            )
            
            if result.get("success", False):
                # 构建成功响应
                response_data = {
                    "deviceId": device_id,
                    "deviceName": device_name,
                    "action": action,
                    "value": value,
                    "newState": result.get("newState", {}),
                    "message": result.get("message", "操作成功")
                }
                
                return ToolExecutionResult(
                    tool_name=self.name,
                    call_id=call_id,
                    status=ToolExecutionStatus.SUCCESS,
                    success=True,
                    message=f"成功{self._get_action_description(action, value)}{device_name}",
                    data=response_data
                )
            else:
                # 构建失败响应
                return ToolExecutionResult(
                    tool_name=self.name,
                    call_id=call_id,
                    status=ToolExecutionStatus.FAILED,
                    success=False,
                    message=result.get("message", "设备控制失败"),
                    error_code=result.get("errorCode", "CONTROL_FAILED")
                )
                
        except Exception as e:
            return ToolExecutionResult(
                tool_name=self.name,
                call_id=call_id,
                status=ToolExecutionStatus.FAILED,
                success=False,
                message=f"设备控制异常: {str(e)}",
                error_code="EXECUTION_ERROR"
            )
    
    def _get_action_description(self, action: str, value: Any = None) -> str:
        """获取动作的中文描述"""
        action_map = {
            "turnOn": "打开",
            "turnOff": "关闭",
            "toggle": "切换",
            "setBrightness": f"设置亮度为{value}%",
            "setTemperature": f"设置温度为{value}°C",
            "setColor": "设置颜色",
            "setMode": "设置模式"
        }
        return action_map.get(action, action)


class DeviceQueryTool(BaseTool):
    """
    设备状态查询工具
    查询设备当前状态、属性等信息
    """
    
    def __init__(self, platform_connector: Optional[PlatformServiceConnector] = None):
        super().__init__(
            name="queryDeviceStatus",
            description="查询智能设备的当前状态和属性信息"
        )
        self.platform_connector = platform_connector or PlatformServiceConnector()
    
    def get_tool_definition(self) -> Dict[str, Any]:
        """获取设备查询工具的Function Calling定义"""
        return {
            "type": "function",
            "function": {
                "name": "queryDeviceStatus",
                "description": "查询智能设备的当前状态、属性和运行信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "deviceId": {
                            "type": "string",
                            "description": "设备ID，如 'light-01', 'ac-02' 等"
                        },
                        "deviceName": {
                            "type": "string",
                            "description": "设备名称，用于用户友好的反馈"
                        },
                        "properties": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "要查询的属性列表，如 ['power', 'brightness', 'temperature']"
                        }
                    },
                    "required": ["deviceId"]
                }
            }
        }
    
    def _get_required_parameters(self) -> List[str]:
        """获取必需参数"""
        return ["deviceId"]
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """验证设备查询参数"""
        # 调用基类验证
        is_valid, error_msg = super().validate_parameters(parameters)
        if not is_valid:
            return is_valid, error_msg
        
        device_id = parameters.get("deviceId")
        properties = parameters.get("properties")
        
        # 验证设备ID格式
        if not device_id or not isinstance(device_id, str):
            return False, "deviceId must be a non-empty string"
        
        # 验证属性列表格式
        if properties is not None:
            if not isinstance(properties, list):
                return False, "properties must be a list of strings"
            
            for prop in properties:
                if not isinstance(prop, str):
                    return False, "All properties must be strings"
        
        return True, None
    
    def check_permissions(
        self, 
        context: Optional[Dict[str, Any]] = None
    ) -> tuple[bool, Optional[str]]:
        """检查查询权限（相对宽松）"""
        if not context:
            return False, "No execution context provided"
        
        # 查询权限相对宽松，只需要基础查询权限
        permissions = context.get("permissions", {})
        if not permissions.get("canQuery", True):  # 默认允许查询
            return False, "Insufficient permissions for device query"
        
        return True, None
    
    async def execute(
        self, 
        call_id: str,
        parameters: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> ToolExecutionResult:
        """执行设备状态查询"""
        
        device_id = parameters["deviceId"]
        device_name = parameters.get("deviceName", device_id)
        properties = parameters.get("properties")
        
        try:
            # 构建扩展参数
            extend_params = None
            if context:
                extend_params = ExtendParams(
                    authorization=context.get("authorization", ""),
                    buildingId=context.get("buildingId", "")
                )
            
            # 调用平台服务查询状态
            result = await self.platform_connector.query_device_status(
                device_id=device_id,
                extend_params=extend_params,
                request_id=call_id
            )
            
            if result.get("success", False):
                devices = result.get("devices", [])
                if devices:
                    device_info = devices[0]  # 取第一个设备信息
                    
                    # 如果指定了特定属性，只返回这些属性
                    if properties:
                        filtered_status = {}
                        device_status = device_info.get("status", {})
                        for prop in properties:
                            if prop in device_status:
                                filtered_status[prop] = device_status[prop]
                        device_info["status"] = filtered_status
                    
                    response_data = {
                        "deviceId": device_id,
                        "deviceName": device_name,
                        "deviceInfo": device_info,
                        "queriedProperties": properties or "all"
                    }
                    
                    return ToolExecutionResult(
                        tool_name=self.name,
                        call_id=call_id,
                        status=ToolExecutionStatus.SUCCESS,
                        success=True,
                        message=f"成功查询{device_name}的状态信息",
                        data=response_data
                    )
                else:
                    return ToolExecutionResult(
                        tool_name=self.name,
                        call_id=call_id,
                        status=ToolExecutionStatus.FAILED,
                        success=False,
                        message=f"未找到设备 {device_name}",
                        error_code="DEVICE_NOT_FOUND"
                    )
            else:
                return ToolExecutionResult(
                    tool_name=self.name,
                    call_id=call_id,
                    status=ToolExecutionStatus.FAILED,
                    success=False,
                    message=result.get("message", "设备查询失败"),
                    error_code=result.get("errorCode", "QUERY_FAILED")
                )
                
        except Exception as e:
            return ToolExecutionResult(
                tool_name=self.name,
                call_id=call_id,
                status=ToolExecutionStatus.FAILED,
                success=False,
                message=f"设备查询异常: {str(e)}",
                error_code="EXECUTION_ERROR"
            )
