"""
工具管理器
统一管理所有Function Calling工具的注册、执行和结果处理
"""
from typing import Dict, Any, List, Optional, Type
import asyncio
import time

from .base_tool import BaseTool, ToolExecutionResult, ToolExecutionStatus
from .device_tools import DeviceControlTool, DeviceQueryTool
from src.connectors import PlatformServiceConnector
from src.logger import get_logger

logger = get_logger(__name__)


class ToolManager:
    """
    工具管理器
    
    职责：
    1. 注册和管理所有可用工具
    2. 提供工具定义给LLM
    3. 执行工具调用
    4. 处理工具执行结果
    5. 工具权限和参数验证
    """
    
    def __init__(self, platform_connector: Optional[PlatformServiceConnector] = None):
        self.platform_connector = platform_connector or PlatformServiceConnector()
        self.tools: Dict[str, BaseTool] = {}
        self.logger = get_logger(__name__)
        
        # 注册默认工具
        self._register_default_tools()
    
    def _register_default_tools(self):
        """注册默认的工具集"""
        # 设备控制工具
        device_control_tool = DeviceControlTool(self.platform_connector)
        self.register_tool(device_control_tool)
        
        # 设备查询工具
        device_query_tool = DeviceQueryTool(self.platform_connector)
        self.register_tool(device_query_tool)
        
        self.logger.info(
            "Default tools registered",
            tool_count=len(self.tools),
            tools=list(self.tools.keys())
        )
    
    def register_tool(self, tool: BaseTool):
        """
        注册工具
        
        Args:
            tool: 工具实例
        """
        if not isinstance(tool, BaseTool):
            raise ValueError("Tool must be an instance of BaseTool")
        
        if tool.name in self.tools:
            self.logger.warning(
                "Tool already registered, replacing",
                tool_name=tool.name
            )
        
        self.tools[tool.name] = tool
        
        self.logger.info(
            "Tool registered",
            tool_name=tool.name,
            tool_description=tool.description
        )
    
    def unregister_tool(self, tool_name: str):
        """
        注销工具
        
        Args:
            tool_name: 工具名称
        """
        if tool_name in self.tools:
            del self.tools[tool_name]
            self.logger.info("Tool unregistered", tool_name=tool_name)
        else:
            self.logger.warning("Tool not found for unregistration", tool_name=tool_name)
    
    def get_tool_definitions(self) -> List[Dict[str, Any]]:
        """
        获取所有工具的Function Calling定义
        
        Returns:
            List[Dict[str, Any]]: 工具定义列表，用于LLM Function Calling
        """
        definitions = []
        
        for tool_name, tool in self.tools.items():
            try:
                definition = tool.get_tool_definition()
                definitions.append(definition)
            except Exception as e:
                self.logger.error(
                    "Failed to get tool definition",
                    tool_name=tool_name,
                    error=str(e)
                )
        
        self.logger.debug(
            "Tool definitions retrieved",
            tool_count=len(definitions)
        )
        
        return definitions
    
    def get_tool_info(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有工具的详细信息
        
        Returns:
            Dict[str, Dict[str, Any]]: 工具信息字典
        """
        tool_info = {}
        
        for tool_name, tool in self.tools.items():
            try:
                tool_info[tool_name] = tool.get_tool_info()
            except Exception as e:
                self.logger.error(
                    "Failed to get tool info",
                    tool_name=tool_name,
                    error=str(e)
                )
                tool_info[tool_name] = {
                    "name": tool_name,
                    "error": str(e)
                }
        
        return tool_info
    
    async def execute_tool_call(
        self,
        tool_name: str,
        call_id: str,
        parameters: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> ToolExecutionResult:
        """
        执行单个工具调用
        
        Args:
            tool_name: 工具名称
            call_id: 调用ID
            parameters: 工具参数
            context: 执行上下文
            
        Returns:
            ToolExecutionResult: 执行结果
        """
        start_time = time.time()
        
        # 检查工具是否存在
        if tool_name not in self.tools:
            return ToolExecutionResult(
                tool_name=tool_name,
                call_id=call_id,
                status=ToolExecutionStatus.FAILED,
                success=False,
                message=f"Tool '{tool_name}' not found",
                error_code="TOOL_NOT_FOUND",
                execution_time_ms=int((time.time() - start_time) * 1000)
            )
        
        tool = self.tools[tool_name]
        
        try:
            self.logger.info(
                "Executing tool call",
                tool_name=tool_name,
                call_id=call_id,
                parameters=parameters
            )
            
            # 执行工具
            result = await tool._execute_with_timing(call_id, parameters, context)
            
            self.logger.info(
                "Tool call completed",
                tool_name=tool_name,
                call_id=call_id,
                success=result.success,
                status=result.status.value,
                execution_time_ms=result.execution_time_ms
            )
            
            return result
            
        except Exception as e:
            execution_time = int((time.time() - start_time) * 1000)
            
            self.logger.error(
                "Tool call execution failed",
                tool_name=tool_name,
                call_id=call_id,
                error=str(e),
                execution_time_ms=execution_time
            )
            
            return ToolExecutionResult(
                tool_name=tool_name,
                call_id=call_id,
                status=ToolExecutionStatus.FAILED,
                success=False,
                message=f"Tool execution failed: {str(e)}",
                error_code="EXECUTION_ERROR",
                execution_time_ms=execution_time
            )
    
    async def execute_tool_calls(
        self,
        tool_calls: List[Dict[str, Any]],
        context: Optional[Dict[str, Any]] = None,
        max_concurrent: int = 5
    ) -> List[ToolExecutionResult]:
        """
        批量执行工具调用
        
        Args:
            tool_calls: 工具调用列表，格式：[{"id": "call_1", "function": {"name": "tool_name", "arguments": {...}}}]
            context: 执行上下文
            max_concurrent: 最大并发数
            
        Returns:
            List[ToolExecutionResult]: 执行结果列表
        """
        if not tool_calls:
            return []
        
        self.logger.info(
            "Executing batch tool calls",
            call_count=len(tool_calls),
            max_concurrent=max_concurrent
        )
        
        # 创建执行任务
        tasks = []
        for tool_call in tool_calls:
            call_id = tool_call.get("id", f"call_{int(time.time() * 1000)}")
            function_info = tool_call.get("function", {})
            tool_name = function_info.get("name")
            parameters = function_info.get("arguments", {})
            
            # 如果参数是字符串，尝试解析为JSON
            if isinstance(parameters, str):
                try:
                    import json
                    parameters = json.loads(parameters)
                except json.JSONDecodeError as e:
                    self.logger.error(
                        "Failed to parse tool parameters",
                        call_id=call_id,
                        tool_name=tool_name,
                        parameters=parameters,
                        error=str(e)
                    )
                    # 创建错误结果
                    error_result = ToolExecutionResult(
                        tool_name=tool_name or "unknown",
                        call_id=call_id,
                        status=ToolExecutionStatus.INVALID_PARAMS,
                        success=False,
                        message=f"Invalid parameters format: {str(e)}",
                        error_code="INVALID_PARAMS"
                    )
                    tasks.append(asyncio.create_task(asyncio.sleep(0, result=error_result)))
                    continue
            
            if not tool_name:
                # 创建错误结果
                error_result = ToolExecutionResult(
                    tool_name="unknown",
                    call_id=call_id,
                    status=ToolExecutionStatus.INVALID_PARAMS,
                    success=False,
                    message="Missing tool name in function call",
                    error_code="INVALID_PARAMS"
                )
                tasks.append(asyncio.create_task(asyncio.sleep(0, result=error_result)))
                continue
            
            # 创建执行任务
            task = self.execute_tool_call(tool_name, call_id, parameters, context)
            tasks.append(task)
        
        # 控制并发执行
        results = []
        for i in range(0, len(tasks), max_concurrent):
            batch_tasks = tasks[i:i + max_concurrent]
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            # 处理异常结果
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    call_index = i + j
                    call_id = tool_calls[call_index].get("id", f"call_{call_index}")
                    tool_name = tool_calls[call_index].get("function", {}).get("name", "unknown")
                    
                    error_result = ToolExecutionResult(
                        tool_name=tool_name,
                        call_id=call_id,
                        status=ToolExecutionStatus.FAILED,
                        success=False,
                        message=f"Execution exception: {str(result)}",
                        error_code="EXECUTION_EXCEPTION"
                    )
                    results.append(error_result)
                else:
                    results.append(result)
        
        # 统计执行结果
        successful_count = sum(1 for r in results if r.success)
        failed_count = len(results) - successful_count
        
        self.logger.info(
            "Batch tool calls completed",
            total_calls=len(results),
            successful_calls=successful_count,
            failed_calls=failed_count
        )
        
        return results
    
    def get_available_tools(self) -> List[str]:
        """
        获取可用工具列表
        
        Returns:
            List[str]: 工具名称列表
        """
        return list(self.tools.keys())
    
    def is_tool_available(self, tool_name: str) -> bool:
        """
        检查工具是否可用
        
        Args:
            tool_name: 工具名称
            
        Returns:
            bool: 是否可用
        """
        return tool_name in self.tools
    
    async def close(self):
        """关闭工具管理器，清理资源"""
        self.logger.info("Closing tool manager")
        
        # 清理平台连接器
        if self.platform_connector:
            await self.platform_connector.close()
        
        # 清空工具注册
        self.tools.clear()
        
        self.logger.info("Tool manager closed")
