"""
工具执行器基类
定义所有Function Calling工具的统一接口
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
import time

from src.logger import get_logger

logger = get_logger(__name__)


class ToolExecutionStatus(Enum):
    """工具执行状态"""
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL_SUCCESS = "partial_success"
    PERMISSION_DENIED = "permission_denied"
    INVALID_PARAMS = "invalid_params"
    TIMEOUT = "timeout"


@dataclass
class ToolExecutionResult:
    """
    工具执行结果标准化数据结构
    """
    tool_name: str
    call_id: str
    status: ToolExecutionStatus
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error_code: Optional[str] = None
    execution_time_ms: Optional[int] = None
    timestamp: Optional[int] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = int(time.time() * 1000)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "tool_name": self.tool_name,
            "call_id": self.call_id,
            "status": self.status.value,
            "success": self.success,
            "message": self.message,
            "data": self.data,
            "error_code": self.error_code,
            "execution_time_ms": self.execution_time_ms,
            "timestamp": self.timestamp
        }


class BaseTool(ABC):
    """
    工具执行器基类
    
    所有Function Calling工具都应该继承此基类
    """
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self.logger = get_logger(f"{__name__}.{name}")
    
    @abstractmethod
    def get_tool_definition(self) -> Dict[str, Any]:
        """
        获取工具定义，用于LLM Function Calling
        
        Returns:
            Dict[str, Any]: OpenAI Function Calling格式的工具定义
        """
        pass
    
    @abstractmethod
    async def execute(
        self, 
        call_id: str,
        parameters: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> ToolExecutionResult:
        """
        执行工具操作
        
        Args:
            call_id: 工具调用ID
            parameters: 工具参数
            context: 执行上下文（如用户权限、建筑ID等）
            
        Returns:
            ToolExecutionResult: 标准化的执行结果
        """
        pass
    
    def validate_parameters(self, parameters: Dict[str, Any]) -> tuple[bool, Optional[str]]:
        """
        验证工具参数
        
        Args:
            parameters: 工具参数
            
        Returns:
            tuple[bool, Optional[str]]: (是否有效, 错误信息)
        """
        # 基础验证逻辑，子类可以重写
        required_params = self._get_required_parameters()
        
        for param in required_params:
            if param not in parameters:
                return False, f"Missing required parameter: {param}"
            
            if parameters[param] is None:
                return False, f"Parameter '{param}' cannot be None"
        
        return True, None
    
    def _get_required_parameters(self) -> List[str]:
        """
        获取必需参数列表
        子类应该重写此方法
        
        Returns:
            List[str]: 必需参数名称列表
        """
        return []
    
    def check_permissions(
        self, 
        context: Optional[Dict[str, Any]] = None
    ) -> tuple[bool, Optional[str]]:
        """
        检查执行权限
        
        Args:
            context: 执行上下文
            
        Returns:
            tuple[bool, Optional[str]]: (是否有权限, 错误信息)
        """
        # 基础权限检查，子类可以重写
        if not context:
            return False, "No execution context provided"
        
        # 检查基础权限字段
        permissions = context.get("permissions", {})
        if not permissions.get("canControl", False):
            return False, "Insufficient permissions for device control"
        
        return True, None
    
    async def _execute_with_timing(
        self,
        call_id: str,
        parameters: Dict[str, Any],
        context: Optional[Dict[str, Any]] = None
    ) -> ToolExecutionResult:
        """
        带时间统计的执行包装器
        """
        start_time = time.time()
        
        try:
            # 参数验证
            is_valid, error_msg = self.validate_parameters(parameters)
            if not is_valid:
                return ToolExecutionResult(
                    tool_name=self.name,
                    call_id=call_id,
                    status=ToolExecutionStatus.INVALID_PARAMS,
                    success=False,
                    message=error_msg,
                    error_code="INVALID_PARAMS",
                    execution_time_ms=int((time.time() - start_time) * 1000)
                )
            
            # 权限检查
            has_permission, permission_error = self.check_permissions(context)
            if not has_permission:
                return ToolExecutionResult(
                    tool_name=self.name,
                    call_id=call_id,
                    status=ToolExecutionStatus.PERMISSION_DENIED,
                    success=False,
                    message=permission_error,
                    error_code="PERMISSION_DENIED",
                    execution_time_ms=int((time.time() - start_time) * 1000)
                )
            
            # 执行工具
            result = await self.execute(call_id, parameters, context)
            result.execution_time_ms = int((time.time() - start_time) * 1000)
            
            self.logger.info(
                "Tool execution completed",
                tool_name=self.name,
                call_id=call_id,
                status=result.status.value,
                execution_time_ms=result.execution_time_ms
            )
            
            return result
            
        except Exception as e:
            execution_time = int((time.time() - start_time) * 1000)
            
            self.logger.error(
                "Tool execution failed",
                tool_name=self.name,
                call_id=call_id,
                error=str(e),
                execution_time_ms=execution_time
            )
            
            return ToolExecutionResult(
                tool_name=self.name,
                call_id=call_id,
                status=ToolExecutionStatus.FAILED,
                success=False,
                message=f"Tool execution failed: {str(e)}",
                error_code="EXECUTION_ERROR",
                execution_time_ms=execution_time
            )
    
    def get_tool_info(self) -> Dict[str, Any]:
        """
        获取工具基本信息
        
        Returns:
            Dict[str, Any]: 工具信息
        """
        return {
            "name": self.name,
            "description": self.description,
            "required_parameters": self._get_required_parameters(),
            "definition": self.get_tool_definition()
        }
