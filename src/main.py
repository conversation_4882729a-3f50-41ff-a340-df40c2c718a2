"""
InxVision Voice Service 主应用
基于FastAPI的语音交互服务，按照分层架构设计
"""
import time
import uuid
from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager

from src.config import settings
from src.logger import configure_logging, get_logger, log_request_response
from src.models import ErrorResponse
from src.controllers.voice_controller import router as voice_router


# 配置日志
configure_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("Starting InxVision Voice Service", version=settings.app_version)
    
    # TODO: 初始化Redis连接
    # TODO: 初始化各种Connector
    
    yield
    
    # 关闭时执行
    logger.info("Shutting down InxVision Voice Service")
    
    # TODO: 清理资源


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="智能语音交互服务，支持语音识别、LLM推理和设备控制",
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(voice_router)


@app.middleware("http")
async def request_logging_middleware(request: Request, call_next):
    """请求日志中间件"""
    start_time = time.time()
    request_id = str(uuid.uuid4())
    
    # 将request_id添加到请求状态中，供后续使用
    request.state.request_id = request_id
    
    try:
        response = await call_next(request)
        process_time = time.time() - start_time
        
        log_request_response(
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            status_code=response.status_code,
            response_time=process_time,
            user_agent=request.headers.get("user-agent", ""),
            client_ip=request.client.host if request.client else ""
        )
        
        return response
        
    except Exception as e:
        process_time = time.time() - start_time
        
        log_request_response(
            request_id=request_id,
            method=request.method,
            url=str(request.url),
            response_time=process_time,
            error=str(e),
            user_agent=request.headers.get("user-agent", ""),
            client_ip=request.client.host if request.client else ""
        )
        
        raise


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    request_id = getattr(request.state, 'request_id', str(uuid.uuid4()))
    
    logger.error(
        "Unhandled exception",
        request_id=request_id,
        error=str(exc),
        path=request.url.path
    )
    
    error_response = ErrorResponse(
        requestId=request_id,
        timestamp=int(time.time() * 1000),
        code="INTERNAL_ERROR",
        msg="服务内部错误，请稍后重试"
    )
    
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=error_response.model_dump(by_alias=True)
    )


@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "service": settings.app_name,
        "version": settings.app_version,
        "timestamp": int(time.time() * 1000)
    }





if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "src.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
