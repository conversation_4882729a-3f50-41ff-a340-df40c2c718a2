"""
连接器基类
定义所有外部服务连接器的通用接口和行为
"""
import asyncio
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any, Union
import httpx
from src.logger import get_logger
from src.config import settings

logger = get_logger(__name__)


class ConnectorError(Exception):
    """连接器异常基类"""
    def __init__(self, message: str, error_code: str = None, original_error: Exception = None):
        self.message = message
        self.error_code = error_code
        self.original_error = original_error
        super().__init__(message)


class TimeoutError(ConnectorError):
    """超时异常"""
    pass


class AuthenticationError(ConnectorError):
    """认证异常"""
    pass


class ServiceUnavailableError(ConnectorError):
    """服务不可用异常"""
    pass


class BaseConnector(ABC):
    """
    连接器基类
    
    提供HTTP客户端封装、错误处理、重试机制等通用功能
    """
    
    def __init__(
        self, 
        base_url: str, 
        timeout: int = 30,
        max_retries: int = 3,
        retry_delay: float = 1.0
    ):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self._client: Optional[httpx.AsyncClient] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_client()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
        
    async def _ensure_client(self):
        """确保HTTP客户端已初始化"""
        if self._client is None:
            self._client = httpx.AsyncClient(
                timeout=httpx.Timeout(self.timeout),
                limits=httpx.Limits(max_connections=10, max_keepalive_connections=5)
            )
    
    async def close(self):
        """关闭HTTP客户端"""
        if self._client:
            await self._client.aclose()
            self._client = None
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        headers: Optional[Dict[str, str]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        data: Optional[Union[str, bytes]] = None,
        params: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        发送HTTP请求，包含重试机制和错误处理
        """
        await self._ensure_client()
        
        url = f"{self.base_url}{endpoint}"
        request_headers = self._build_headers(headers)
        
        for attempt in range(self.max_retries + 1):
            try:
                logger.info(
                    "Making HTTP request",
                    method=method,
                    url=url,
                    attempt=attempt + 1,
                    request_id=request_id
                )
                
                response = await self._client.request(
                    method=method,
                    url=url,
                    headers=request_headers,
                    json=json_data,
                    content=data,
                    params=params
                )
                
                # 检查响应状态
                if response.status_code == 200:
                    result = response.json()
                    logger.info(
                        "HTTP request successful",
                        status_code=response.status_code,
                        request_id=request_id
                    )
                    return result
                elif response.status_code == 401:
                    raise AuthenticationError(
                        f"Authentication failed: {response.text}",
                        error_code="AUTH_FAILED"
                    )
                elif response.status_code >= 500:
                    raise ServiceUnavailableError(
                        f"Service unavailable: {response.status_code} {response.text}",
                        error_code="SERVICE_UNAVAILABLE"
                    )
                else:
                    raise ConnectorError(
                        f"HTTP request failed: {response.status_code} {response.text}",
                        error_code="HTTP_ERROR"
                    )
                    
            except httpx.TimeoutException as e:
                if attempt == self.max_retries:
                    raise TimeoutError(
                        f"Request timeout after {self.max_retries + 1} attempts",
                        error_code="TIMEOUT",
                        original_error=e
                    )
                logger.warning(
                    "Request timeout, retrying",
                    attempt=attempt + 1,
                    max_retries=self.max_retries,
                    request_id=request_id
                )
                await asyncio.sleep(self.retry_delay * (attempt + 1))
                
            except (httpx.ConnectError, httpx.NetworkError) as e:
                if attempt == self.max_retries:
                    raise ServiceUnavailableError(
                        f"Network error after {self.max_retries + 1} attempts: {str(e)}",
                        error_code="NETWORK_ERROR",
                        original_error=e
                    )
                logger.warning(
                    "Network error, retrying",
                    error=str(e),
                    attempt=attempt + 1,
                    max_retries=self.max_retries,
                    request_id=request_id
                )
                await asyncio.sleep(self.retry_delay * (attempt + 1))
                
            except Exception as e:
                logger.error(
                    "Unexpected error in HTTP request",
                    error=str(e),
                    request_id=request_id
                )
                raise ConnectorError(
                    f"Unexpected error: {str(e)}",
                    error_code="UNEXPECTED_ERROR",
                    original_error=e
                )
    
    def _build_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """构建请求头"""
        headers = {
            "Content-Type": "application/json",
            "User-Agent": f"{settings.app_name}/{settings.app_version}"
        }
        
        if additional_headers:
            headers.update(additional_headers)
            
        return headers
    
    @abstractmethod
    async def health_check(self) -> bool:
        """
        健康检查
        
        Returns:
            bool: 服务是否健康
        """
        pass
    
    @abstractmethod
    def get_service_name(self) -> str:
        """
        获取服务名称
        
        Returns:
            str: 服务名称
        """
        pass
