"""
阿里云ASR远程服务
基于阿里云语音识别REST API实现
"""
import http.client
import json
import asyncio
import ssl
import tempfile
import os
from typing import Optional, Dict, Any, Union
from urllib.parse import urlencode
from src.logger import get_logger
from src.config import settings

logger = get_logger(__name__)


class AliASRRemoteService:
    """
    阿里云ASR远程服务
    
    支持：
    1. 文件路径输入
    2. 直接音频数据输入
    3. 异步调用
    4. 完整的错误处理
    """
    
    def __init__(self, app_key: Optional[str] = None, token: Optional[str] = None):
        """
        初始化阿里云ASR服务
        
        Args:
            app_key: 阿里云应用Key
            token: 阿里云服务鉴权Token
        """
        self.app_key = app_key or getattr(settings, 'ali_asr_app_key', None)
        self.token = token or getattr(settings, 'ali_asr_token', None)
        self.host = 'nls-gateway-cn-shanghai.aliyuncs.com'
        self.base_url = '/stream/v1/asr'
        
        # 默认参数
        self.default_params = {
            'format': 'pcm',
            'sample_rate': 16000,
            'enable_punctuation_prediction': True,
            'enable_inverse_text_normalization': True,
            'enable_voice_detection': False
        }
        
        logger.info(
            "AliASRRemoteService initialized",
            app_key_configured=bool(self.app_key),
            token_configured=bool(self.token),
            host=self.host
        )
    
    async def recognize_from_file(
        self, 
        audio_file_path: str, 
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        从音频文件进行语音识别
        
        Args:
            audio_file_path: 音频文件路径
            params: 识别参数
            
        Returns:
            Dict[str, Any]: 识别结果
        """
        if not os.path.exists(audio_file_path):
            raise FileNotFoundError(f"Audio file not found: {audio_file_path}")
        
        # 读取音频文件
        with open(audio_file_path, 'rb') as f:
            audio_content = f.read()
        
        return await self.recognize_from_data(audio_content, params)
    
    async def recognize_from_data(
        self, 
        audio_data: bytes, 
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        从音频数据进行语音识别
        
        Args:
            audio_data: 音频数据字节
            params: 识别参数
            
        Returns:
            Dict[str, Any]: 识别结果
        """
        if not self.app_key or not self.token:
            raise ValueError("Ali ASR app_key and token must be configured")
        
        if not audio_data:
            raise ValueError("Audio data is empty")
        
        # 合并参数
        request_params = {**self.default_params}
        if params:
            request_params.update(params)
        
        logger.info(
            "Starting Ali ASR recognition",
            audio_size=len(audio_data),
            params=request_params
        )
        
        try:
            # 在线程池中执行同步HTTP请求
            result = await asyncio.get_event_loop().run_in_executor(
                None, 
                self._sync_recognize, 
                audio_data, 
                request_params
            )
            
            # 记录日志时确保中文字符正确显示
            result_text = result.get('text', '')
            logger.info(
                f"Ali ASR recognition completed: {result_text}",
                success=result.get('success', False),
                status=result.get('status'),
                text_length=len(result_text)
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "Ali ASR recognition failed",
                error=str(e),
                audio_size=len(audio_data)
            )
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'status': None,
                'raw_response': None
            }
    
    def _sync_recognize(self, audio_data: bytes, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        同步执行语音识别（在线程池中调用）
        
        Args:
            audio_data: 音频数据
            params: 识别参数
            
        Returns:
            Dict[str, Any]: 识别结果
        """
        # 构建请求URL
        request_url = self._build_request_url(params)
        
        # 设置请求头
        headers = {
            'X-NLS-Token': self.token,
            'Content-type': 'application/octet-stream',
            'Content-Length': str(len(audio_data))
        }
        
        conn = None
        try:
            # 创建HTTPS连接
            conn = http.client.HTTPSConnection(
                self.host,
                timeout=30,
                context=ssl.create_default_context()
            )
            
            # 发送请求
            conn.request(
                method='POST',
                url=request_url,
                body=audio_data,
                headers=headers
            )
            
            # 获取响应
            response = conn.getresponse()
            response_body = response.read()
            
            logger.debug(
                "Ali ASR HTTP response",
                status=response.status,
                reason=response.reason,
                response_size=len(response_body)
            )
            
            # 解析响应
            return self._parse_response(response.status, response_body)
            
        except Exception as e:
            logger.error(
                "Ali ASR HTTP request failed",
                error=str(e),
                host=self.host,
                url=request_url
            )
            raise
        finally:
            if conn:
                conn.close()
    
    def _build_request_url(self, params: Dict[str, Any]) -> str:
        """
        构建请求URL
        
        Args:
            params: 请求参数
            
        Returns:
            str: 完整的请求URL
        """
        # 添加app_key
        url_params = {'appkey': self.app_key}
        
        # 添加其他参数
        for key, value in params.items():
            if isinstance(value, bool):
                url_params[key] = 'true' if value else 'false'
            else:
                url_params[key] = str(value)
        
        # 构建完整URL
        query_string = urlencode(url_params)
        request_url = f"{self.base_url}?{query_string}"
        
        logger.debug(
            "Built request URL",
            url=request_url,
            params=url_params
        )
        
        return request_url
    
    def _parse_response(self, status_code: int, response_body: bytes) -> Dict[str, Any]:
        """
        解析阿里云ASR响应
        
        Args:
            status_code: HTTP状态码
            response_body: 响应体
            
        Returns:
            Dict[str, Any]: 解析后的结果
        """
        try:
            # 解析JSON响应
            response_json = json.loads(response_body.decode('utf-8'))
            
            # 检查阿里云API状态
            api_status = response_json.get('status')
            
            if api_status == 20000000:  # 成功
                result_text = response_json.get('result', '')
                return {
                    'success': True,
                    'text': result_text,
                    'status': api_status,
                    'raw_response': response_json
                }
            else:
                # API返回错误
                error_message = response_json.get('message', 'Unknown error')
                logger.warning(
                    "Ali ASR API returned error",
                    status=api_status,
                    message=error_message,
                    response=response_json
                )
                return {
                    'success': False,
                    'error': f"Ali ASR API error: {error_message} (status: {api_status})",
                    'text': '',
                    'status': api_status,
                    'raw_response': response_json
                }
                
        except json.JSONDecodeError as e:
            # JSON解析失败
            logger.error(
                "Failed to parse Ali ASR response as JSON",
                error=str(e),
                response_body=response_body[:200]  # 只记录前200字节
            )
            return {
                'success': False,
                'error': f"Invalid JSON response: {str(e)}",
                'text': '',
                'status': None,
                'raw_response': response_body.decode('utf-8', errors='ignore')
            }
        except Exception as e:
            # 其他解析错误
            logger.error(
                "Failed to parse Ali ASR response",
                error=str(e),
                status_code=status_code
            )
            return {
                'success': False,
                'error': f"Response parsing error: {str(e)}",
                'text': '',
                'status': None,
                'raw_response': response_body.decode('utf-8', errors='ignore')
            }
    
    async def recognize_from_base64(
        self, 
        base64_data: str, 
        params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        从Base64编码的音频数据进行语音识别
        
        Args:
            base64_data: Base64编码的音频数据
            params: 识别参数
            
        Returns:
            Dict[str, Any]: 识别结果
        """
        import base64
        
        try:
            # 解码Base64数据
            audio_data = base64.b64decode(base64_data)
            return await self.recognize_from_data(audio_data, params)
        except Exception as e:
            logger.error(
                "Failed to decode base64 audio data",
                error=str(e),
                data_length=len(base64_data)
            )
            return {
                'success': False,
                'error': f"Base64 decode error: {str(e)}",
                'text': '',
                'status': None,
                'raw_response': None
            }
    
    def is_configured(self) -> bool:
        """
        检查服务是否已正确配置
        
        Returns:
            bool: 是否已配置
        """
        return bool(self.app_key and self.token)
    
    def get_config_info(self) -> Dict[str, Any]:
        """
        获取配置信息（用于调试）
        
        Returns:
            Dict[str, Any]: 配置信息
        """
        return {
            'app_key_configured': bool(self.app_key),
            'token_configured': bool(self.token),
            'host': self.host,
            'base_url': self.base_url,
            'default_params': self.default_params
        }
