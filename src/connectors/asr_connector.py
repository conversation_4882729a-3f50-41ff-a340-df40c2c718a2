"""
ASR连接器 - 语音识别服务连接器
当前为模拟实现，返回预设的测试文本
"""
import asyncio
import base64
import random
import os
import struct
import time
from pathlib import Path
from typing import Optional, Dict, Any
from src.connectors.base_connector import BaseConnector, ConnectorError
from src.connectors.asr_ali_remote_service import AliASRRemoteService
from src.logger import get_logger
from src.config import settings

logger = get_logger(__name__)


class ASRConnector(BaseConnector):
    """
    ASR连接器
    
    职责：
    1. 接收Base64编码的音频数据
    2. 调用ASR服务进行语音识别
    3. 返回识别后的文本结果
    
    当前为模拟实现，支持多种测试场景
    """
    
    def __init__(self):
        # 模拟实现不需要真实的URL
        super().__init__(
            base_url=settings.asr_api_url or "http://mock-asr-service",
            timeout=settings.llm_timeout,  # 复用LLM的超时配置
            max_retries=2
        )
        
        # 初始化阿里云ASR服务
        self.ali_asr_service = AliASRRemoteService()

        # 预设的测试场景文本
        self.test_scenarios = [
            # 设备控制场景
            "把会议室的灯打开",
            "关闭办公室的空调",
            "调高客厅的温度",
            "把所有的灯都关掉",
            "开启三楼的照明系统",
            
            # 查询场景
            "会议室的温度是多少",
            "空调现在是什么状态",
            "今天的用电量怎么样",
            "哪些设备还在运行",
            
            # 复杂场景
            "把会议室A的灯打开，空调调到26度",
            "关闭三楼所有的照明设备",
            "如果温度超过28度就开启空调",
            
            # 日常对话
            "你好",
            "今天天气怎么样",
            "现在几点了",
            "谢谢你的帮助"
        ]
    
    async def speech_to_text(
        self, 
        audio_data: str, 
        audio_format: Optional[Dict[str, Any]] = None,
        request_id: Optional[str] = None
    ) -> str:
        """
        语音转文本
        
        Args:
            audio_data: Base64编码的音频数据
            audio_format: 音频格式信息
            request_id: 请求ID
            
        Returns:
            str: 识别后的文本
        """
        logger.info(
            "Starting ASR processing",
            audio_length=len(audio_data) if audio_data else 0,
            audio_format=audio_format,
            request_id=request_id
        )
        
        try:
            # 验证音频数据
            if not audio_data:
                raise ConnectorError("Audio data is empty", error_code="EMPTY_AUDIO")
            
            # 验证Base64格式
            try:
                # 预处理Base64字符串
                cleaned_audio_data = self._clean_base64_data(audio_data)
                decoded_data = base64.b64decode(cleaned_audio_data, validate=True)

                # 保存音频文件为WAV格式，方便验证测试
                saved_file_path = await self._save_audio_as_wav(decoded_data, request_id)

                logger.info(
                    "Base64 audio data validated and saved",
                    original_length=len(audio_data),
                    cleaned_length=len(cleaned_audio_data),
                    decoded_size=len(decoded_data),
                    saved_file_path=saved_file_path,
                    request_id=request_id
                )

            except Exception as e:
                logger.error(
                    "Base64 validation failed",
                    error=str(e),
                    audio_data_length=len(audio_data),
                    audio_data_preview=audio_data[:50] + "..." if len(audio_data) > 50 else audio_data,
                    request_id=request_id
                )
                raise ConnectorError(f"Invalid Base64 audio data: {str(e)}", error_code="INVALID_AUDIO_FORMAT")
            
            # 尝试使用阿里云ASR服务
            if settings.ali_asr_enabled and self.ali_asr_service.is_configured():
                logger.info(
                    "Using Ali ASR service for recognition",
                    request_id=request_id
                )

                try:
                    # 使用阿里云ASR服务进行识别
                    ali_result = await self.ali_asr_service.recognize_from_base64(
                        base64_data=cleaned_audio_data,
                        params={
                            'format': 'pcm',
                            'sample_rate': audio_format.get('sampleRate', 16000) if audio_format else 16000,
                            'enable_punctuation_prediction': True,
                            'enable_inverse_text_normalization': True
                        }
                    )

                    if ali_result['success']:
                        text_result = ali_result['text']
                        processing_time = 0  # 实际处理时间由阿里云服务决定

                        logger.info(
                            f"Ali ASR recognition successful: {text_result}",
                            ali_status=ali_result.get('status'),
                            text_length=len(text_result),
                            request_id=request_id
                        )
                    else:
                        # 阿里云ASR失败，记录日志并抛出异常
                        logger.error(
                            "Ali ASR recognition failed",
                            error=ali_result.get('error'),
                            ali_status=ali_result.get('status'),
                            request_id=request_id
                        )
                        raise ConnectorError(
                            f"Ali ASR recognition failed: {ali_result.get('error')}",
                            error_code="ALI_ASR_RECOGNITION_FAILED",
                            original_error=ali_result.get('error')
                        )

                except ConnectorError:
                    # 重新抛出ConnectorError
                    raise
                except Exception as e:
                    # 阿里云ASR异常，记录日志并抛出异常
                    logger.error(
                        "Ali ASR service exception",
                        error=str(e),
                        request_id=request_id
                    )
                    raise ConnectorError(
                        f"Ali ASR service exception: {str(e)}",
                        error_code="ALI_ASR_SERVICE_EXCEPTION",
                        original_error=e
                    )
            else:
                # 使用模拟ASR服务
                logger.info(
                    "Using mock ASR service",
                    ali_asr_enabled=settings.ali_asr_enabled,
                    ali_asr_configured=self.ali_asr_service.is_configured(),
                    request_id=request_id
                )

                # 模拟ASR处理时间
                processing_time = random.uniform(0.5, 2.0)
                await asyncio.sleep(processing_time)

                # 根据音频长度选择不同的测试文本
                text_result = self._select_test_text(audio_data, request_id)

            service_type = "ali_asr" if (settings.ali_asr_enabled and self.ali_asr_service.is_configured()) else "mock"
            logger.info(
                f"ASR processing completed: {text_result}",
                processing_time=processing_time,
                service_type=service_type,
                text_length=len(text_result),
                request_id=request_id
            )

            return text_result
            
        except ConnectorError:
            raise
        except Exception as e:
            logger.error(
                "ASR processing failed",
                error=str(e),
                request_id=request_id
            )
            raise ConnectorError(
                f"ASR processing failed: {str(e)}",
                error_code="ASR_PROCESSING_ERROR",
                original_error=e
            )

    def _clean_base64_data(self, audio_data: str) -> str:
        """
        清理Base64数据，处理常见的格式问题

        Args:
            audio_data: 原始Base64字符串

        Returns:
            str: 清理后的Base64字符串
        """
        # 移除可能的前缀（如data:audio/wav;base64,）
        if ',' in audio_data:
            audio_data = audio_data.split(',', 1)[1]

        # 移除所有空白字符（空格、换行符、制表符等）
        audio_data = ''.join(audio_data.split())

        # 确保Base64字符串长度是4的倍数（添加padding）
        missing_padding = len(audio_data) % 4
        if missing_padding:
            # 如果余数是1，这是无效的Base64，可能数据被截断了
            if missing_padding == 1:
                # 尝试移除最后一个字符，然后重新计算padding
                audio_data = audio_data[:-1]
                missing_padding = len(audio_data) % 4
                if missing_padding:
                    audio_data += '=' * (4 - missing_padding)
            else:
                audio_data += '=' * (4 - missing_padding)

        # 验证字符集（Base64只包含A-Z, a-z, 0-9, +, /, =）
        # 注意：某些Base64编码可能使用URL安全字符（-和_代替+和/）
        import re
        if not re.match(r'^[A-Za-z0-9+/\-_]*={0,2}$', audio_data):
            raise ValueError("Contains invalid Base64 characters")

        return audio_data

    def _select_test_text(self, audio_data: str, request_id: Optional[str] = None) -> str:
        """
        根据音频数据特征选择合适的测试文本
        
        Args:
            audio_data: Base64编码的音频数据
            request_id: 请求ID
            
        Returns:
            str: 选择的测试文本
        """
        # 根据音频数据长度和请求ID的特征来选择文本
        # 这样可以让测试更加可预测
        
        audio_length = len(audio_data)
        
        # 根据音频长度选择不同类型的文本
        if audio_length < 1000:
            # 短音频 - 简单指令
            category_start = 0
            category_end = 5
        elif audio_length < 5000:
            # 中等音频 - 查询指令
            category_start = 5
            category_end = 9
        elif audio_length < 10000:
            # 长音频 - 复杂指令
            category_start = 9
            category_end = 12
        else:
            # 很长音频 - 日常对话
            category_start = 12
            category_end = len(self.test_scenarios)
        
        # 使用请求ID的哈希值来确保相同请求返回相同结果
        if request_id:
            seed = hash(request_id) % (category_end - category_start)
            index = category_start + seed
        else:
            index = random.randint(category_start, category_end - 1)
        
        selected_text = self.test_scenarios[index]
        
        logger.debug(
            "Selected test text",
            audio_length=audio_length,
            category_range=f"{category_start}-{category_end}",
            selected_index=index,
            selected_text=selected_text,
            request_id=request_id
        )
        
        return selected_text
    
    async def health_check(self) -> bool:
        """
        健康检查

        Returns:
            bool: 服务是否健康
        """
        if settings.ali_asr_enabled and self.ali_asr_service.is_configured():
            # 对于阿里云ASR，检查配置是否正确
            logger.info("ASR health check - Ali ASR service configured")
            return True
        else:
            # 模拟实现总是健康
            logger.info("ASR health check - mock implementation always healthy")
            return True
    
    def get_service_name(self) -> str:
        """
        获取服务名称

        Returns:
            str: 服务名称
        """
        if settings.ali_asr_enabled and self.ali_asr_service.is_configured():
            return "ASR Service (Ali Cloud)"
        else:
            return "ASR Service (Mock)"
    
    async def get_supported_formats(self) -> Dict[str, Any]:
        """
        获取支持的音频格式
        
        Returns:
            Dict[str, Any]: 支持的音频格式信息
        """
        return {
            "sample_rates": [16000, 8000],
            "channels": [1, 2],
            "encodings": ["PCM_16BIT", "PCM_8BIT"],
            "formats": ["WAV", "MP3", "AAC"]
        }
    
    def set_test_scenario(self, scenario_text: str):
        """
        设置特定的测试场景文本（用于测试）
        
        Args:
            scenario_text: 要返回的测试文本
        """
        if scenario_text not in self.test_scenarios:
            self.test_scenarios.insert(0, scenario_text)
        
        logger.info(
            "Test scenario set",
            scenario_text=scenario_text
        )

    async def _save_audio_as_wav(self, pcm_data: bytes, request_id: Optional[str] = None) -> Optional[str]:
        """
        将PCM音频数据保存为WAV文件

        参考Java实现：processAudioData方法
        音频参数：16000Hz, 16-bit, 单声道

        Args:
            pcm_data: PCM音频数据
            request_id: 请求ID，用于生成文件名

        Returns:
            str: 保存的文件路径，失败时返回None
        """
        try:
            # 1. 创建WAV文件头
            wav_header = self._create_wav_header(len(pcm_data))

            # 2. 合并WAV头和PCM数据
            wav_data = wav_header + pcm_data

            # 3. 创建保存目录
            audio_dir = Path("audio_files")
            audio_dir.mkdir(exist_ok=True)

            # 4. 生成文件名并保存
            timestamp = int(time.time() * 1000)
            file_name = f"audio_{request_id or 'unknown'}_{timestamp}.wav"
            file_path = audio_dir / file_name

            # 5. 写入文件
            with open(file_path, 'wb') as f:
                f.write(wav_data)

            logger.info(
                "Audio file saved successfully",
                file_path=str(file_path),
                file_size=len(wav_data),
                pcm_size=len(pcm_data),
                request_id=request_id
            )

            return str(file_path)

        except Exception as e:
            logger.error(
                "Failed to save audio file",
                error=str(e),
                pcm_size=len(pcm_data),
                request_id=request_id
            )
            return None

    def _create_wav_header(self, pcm_data_length: int) -> bytes:
        """
        创建WAV文件头

        参考Java实现：createWavHeader方法
        音频参数：16000Hz, 16-bit, 单声道

        Args:
            pcm_data_length: PCM数据长度

        Returns:
            bytes: WAV文件头字节数组（44字节）
        """
        # 音频参数
        sample_rate = 16000    # 采样率
        bits_per_sample = 16   # 位深
        channels = 1           # 声道数
        byte_rate = sample_rate * channels * bits_per_sample // 8  # 字节率
        block_align = channels * bits_per_sample // 8              # 块对齐

        # WAV文件头总共44字节
        header = bytearray(44)

        # RIFF头 (12字节)
        header[0:4] = b'RIFF'
        self._write_int_le(header, 4, 36 + pcm_data_length)  # 文件大小 - 8
        header[8:12] = b'WAVE'

        # fmt子块 (24字节)
        header[12:16] = b'fmt '
        self._write_int_le(header, 16, 16)                    # fmt子块大小
        self._write_short_le(header, 20, 1)                   # 音频格式 (1 = PCM)
        self._write_short_le(header, 22, channels)            # 声道数
        self._write_int_le(header, 24, sample_rate)           # 采样率
        self._write_int_le(header, 28, byte_rate)             # 字节率
        self._write_short_le(header, 32, block_align)         # 块对齐
        self._write_short_le(header, 34, bits_per_sample)     # 位深

        # data子块头 (8字节)
        header[36:40] = b'data'
        self._write_int_le(header, 40, pcm_data_length)       # PCM数据大小

        return bytes(header)

    def _write_int_le(self, data: bytearray, offset: int, value: int) -> None:
        """
        将int值写入字节数组 (小端序)

        参考Java实现：writeInt方法
        """
        data[offset] = value & 0xFF
        data[offset + 1] = (value >> 8) & 0xFF
        data[offset + 2] = (value >> 16) & 0xFF
        data[offset + 3] = (value >> 24) & 0xFF

    def _write_short_le(self, data: bytearray, offset: int, value: int) -> None:
        """
        将short值写入字节数组 (小端序)

        参考Java实现：writeShort方法
        """
        data[offset] = value & 0xFF
        data[offset + 1] = (value >> 8) & 0xFF
