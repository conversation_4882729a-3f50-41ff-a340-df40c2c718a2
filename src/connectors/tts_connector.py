"""
TTS连接器 - 文本转语音服务连接器
支持阿里云TTS和模拟TTS服务
"""
import asyncio
import hashlib
import random
from typing import Optional, Dict, Any
from src.connectors.base_connector import BaseConnector, ConnectorError
from src.connectors.tts_ali_remote_service import AliTTSRemoteService
from src.logger import get_logger
from src.config import settings

logger = get_logger(__name__)


class TTSConnector(BaseConnector):
    """
    TTS连接器
    
    职责：
    1. 接收文本内容
    2. 调用TTS服务生成语音
    3. 返回音频文件URL或Base64数据
    
    当前为模拟实现，返回模拟的音频URL
    """
    
    def __init__(self):
        # 模拟实现不需要真实的URL
        super().__init__(
            base_url=settings.tts_api_url or "http://mock-tts-service",
            timeout=settings.llm_timeout,  # 复用LLM的超时配置
            max_retries=2
        )

        # 初始化阿里云TTS服务
        self.ali_tts_service = AliTTSRemoteService()

        # 模拟的CDN基础URL
        self.mock_cdn_base = f"http://{settings.tts_http_server_host}"
        
        # 支持的语音配置
        self.voice_configs = {
            "default": {
                "voice_id": "xiaoyun",
                "language": "zh-CN",
                "speed": 1.0,
                "pitch": 0.0,
                "volume": 1.0
            },
            "formal": {
                "voice_id": "xiaogang", 
                "language": "zh-CN",
                "speed": 0.9,
                "pitch": -0.1,
                "volume": 1.0
            },
            "friendly": {
                "voice_id": "xiaomei",
                "language": "zh-CN", 
                "speed": 1.1,
                "pitch": 0.1,
                "volume": 1.0
            }
        }
    
    async def text_to_speech(
        self,
        text: str,
        voice_config: str = "default",
        output_format: str = "mp3",
        return_base64: bool = False,
        request_id: Optional[str] = None
    ) -> Optional[str]:
        """
        文本转语音
        
        Args:
            text: 要转换的文本
            voice_config: 语音配置 (default/formal/friendly)
            output_format: 输出格式 (mp3/wav)
            return_base64: 是否返回Base64编码的音频数据
            request_id: 请求ID
            
        Returns:
            Optional[str]: 音频文件URL或Base64数据，None表示不生成音频
        """
        logger.info(
            "Starting TTS processing",
            text_length=len(text),
            voice_config=voice_config,
            output_format=output_format,
            return_base64=return_base64,
            request_id=request_id
        )
        
        try:
            # 验证输入
            if not text or not text.strip():
                raise ConnectorError("Text is empty", error_code="EMPTY_TEXT")
            
            if len(text) > 5000:
                raise ConnectorError("Text too long (max 5000 characters)", error_code="TEXT_TOO_LONG")
            
            # 处理voice_config参数
            if isinstance(voice_config, dict):
                # 如果是字典，保持原样用于阿里云TTS
                voice_config_key = voice_config.get('voice', 'default')
            else:
                # 如果是字符串，用于模拟TTS
                voice_config_key = voice_config or "default"

            if voice_config_key not in self.voice_configs:
                logger.warning(
                    "Unknown voice config, using default",
                    voice_config=voice_config,
                    voice_config_key=voice_config_key,
                    request_id=request_id
                )
                voice_config_key = "default"
                if isinstance(voice_config, dict):
                    voice_config['voice'] = 'xiaoyun'  # 阿里云默认发音人
            
            # 模拟TTS处理时间（基于文本长度）
            #processing_time = min(0.5 + len(text) * 0.01, 5.0)
            processing_time = 0.8
            await asyncio.sleep(processing_time)
            
            # 生成模拟音频URL或Base64数据
            if return_base64:
                audio_result = self._generate_mock_base64_audio(text, voice_config_key, output_format)
            else:
                # 如果配置了阿里云TTS，优先使用阿里云TTS服务
                if settings.ali_tts_enabled and self.ali_tts_service.is_configured():
                    try:
                        logger.info(
                            "Using Ali TTS service for synthesis",
                            request_id=request_id
                        )

                        # 构建TTS参数
                        tts_params = {
                            'format': 'wav' if output_format == 'wav' else 'mp3',
                            'sample_rate': 16000,
                            'voice': voice_config.get('voice', 'xiaoyun'),
                            'volume': voice_config.get('volume', 50),
                            'speech_rate': voice_config.get('speech_rate', 0),
                            'pitch_rate': voice_config.get('pitch_rate', 0)
                        }

                        # 调用阿里云TTS服务
                        ali_result = await self.ali_tts_service.text_to_speech(
                            text=text,
                            params=tts_params,
                            use_post=True
                        )

                        if ali_result['success']:
                            audio_result = ali_result['audio_url']
                            processing_time = 0  # 实际处理时间由阿里云服务决定

                            logger.info(
                                f"Ali TTS synthesis successful: {audio_result}",
                                audio_file=ali_result.get('audio_file', ''),
                                file_size=ali_result.get('file_size', 0),
                                request_id=request_id
                            )
                        else:
                            # 阿里云TTS失败，记录日志并抛出异常
                            logger.error(
                                "Ali TTS synthesis failed",
                                error=ali_result.get('error'),
                                message=ali_result.get('message'),
                                request_id=request_id
                            )
                            raise ConnectorError(
                                f"Ali TTS synthesis failed: {ali_result.get('error')}",
                                error_code="ALI_TTS_SYNTHESIS_FAILED",
                                original_error=ali_result.get('error')
                            )

                    except ConnectorError:
                        # 重新抛出ConnectorError
                        raise
                    except Exception as e:
                        # 阿里云TTS异常，记录日志并抛出异常
                        logger.error(
                            "Ali TTS service exception",
                            error=str(e),
                            request_id=request_id
                        )
                        raise ConnectorError(
                            f"Ali TTS service exception: {str(e)}",
                            error_code="ALI_TTS_SERVICE_EXCEPTION",
                            original_error=e
                        )
                else:
                    # 使用模拟TTS实现
                    logger.info(
                        "Using mock TTS service",
                        ali_tts_configured=self.ali_tts_service.is_configured(),
                        ali_tts_enabled=settings.ali_tts_enabled,
                        request_id=request_id
                    )
                    audio_result = self._generate_mock_audio_url(text, voice_config_key, output_format)
            
            logger.info(
                "TTS processing completed",
                result_type="base64" if return_base64 else "url",
                processing_time=processing_time,
                request_id=request_id
            )
            
            return audio_result
            
        except ConnectorError:
            raise
        except Exception as e:
            logger.error(
                "TTS processing failed",
                error=str(e),
                request_id=request_id
            )
            raise ConnectorError(
                f"TTS processing failed: {str(e)}",
                error_code="TTS_PROCESSING_ERROR",
                original_error=e
            )
    
    def _generate_mock_audio_url(
        self, 
        text: str, 
        voice_config: str, 
        output_format: str
    ) -> str:
        """
        生成模拟音频URL
        
        Args:
            text: 文本内容
            voice_config: 语音配置
            output_format: 输出格式
            
        Returns:
            str: 模拟的音频URL
        """
        # 使用文本内容生成一致的哈希值
        text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()[:8]
        
        # 构建模拟URL
        filename = f"tts_{text_hash}_{voice_config}.{output_format}"
        # url = f"{self.mock_cdn_base}/{filename}"
        url = "http://192.168.3.74/ttspro.mp3"
        
        logger.debug(
            "Generated mock audio URL",
            text_hash=text_hash,
            voice_config=voice_config,
            url=url
        )
        
        return url
    
    def _generate_mock_base64_audio(
        self, 
        text: str, 
        voice_config: str, 
        output_format: str
    ) -> str:
        """
        生成模拟Base64音频数据
        
        Args:
            text: 文本内容
            voice_config: 语音配置
            output_format: 输出格式
            
        Returns:
            str: 模拟的Base64音频数据
        """
        # 生成基于文本内容的伪随机数据
        text_hash = hashlib.md5(text.encode('utf-8')).hexdigest()
        
        # 模拟音频文件头和数据
        if output_format == "wav":
            # WAV文件头模拟
            mock_data = f"RIFF{text_hash}WAVEfmt data{text_hash * 10}"
        else:
            # MP3文件头模拟
            mock_data = f"ID3{text_hash}MPEG{text_hash * 8}"
        
        # 转换为Base64
        import base64
        base64_data = base64.b64encode(mock_data.encode('utf-8')).decode('utf-8')
        
        logger.debug(
            "Generated mock base64 audio",
            text_hash=text_hash[:8],
            voice_config=voice_config,
            data_length=len(base64_data)
        )
        
        return base64_data
    
    async def get_voice_list(self) -> Dict[str, Any]:
        """
        获取可用的语音列表
        
        Returns:
            Dict[str, Any]: 语音配置信息
        """
        logger.info("Getting voice list")
        
        # 模拟获取语音列表的延迟
        await asyncio.sleep(0.1)
        
        return {
            "voices": self.voice_configs,
            "supported_formats": ["mp3", "wav"],
            "supported_languages": ["zh-CN", "en-US"],
            "max_text_length": 5000
        }
    
    async def health_check(self) -> bool:
        """
        健康检查
        
        Returns:
            bool: 服务是否健康（模拟实现总是返回True）
        """
        logger.info("TTS health check - mock implementation always healthy")
        return True
    
    def get_service_name(self) -> str:
        """
        获取服务名称

        Returns:
            str: 服务名称
        """
        if settings.ali_tts_enabled and self.ali_tts_service.is_configured():
            return "TTS Service (Ali Cloud)"
        else:
            return "TTS Service (Mock)"
    
    def estimate_audio_duration(self, text: str, voice_config: str = "default") -> float:
        """
        估算音频时长
        
        Args:
            text: 文本内容
            voice_config: 语音配置
            
        Returns:
            float: 估算的音频时长（秒）
        """
        # 基于文本长度和语速估算时长
        config = self.voice_configs.get(voice_config, self.voice_configs["default"])
        speed = config.get("speed", 1.0)
        
        # 中文大约每分钟200-300字，这里按250字/分钟计算
        base_duration = len(text) / 250 * 60
        
        # 根据语速调整
        estimated_duration = base_duration / speed
        
        logger.debug(
            "Estimated audio duration",
            text_length=len(text),
            speed=speed,
            estimated_duration=estimated_duration
        )
        
        return estimated_duration
    
    def set_mock_cdn_base(self, cdn_base: str):
        """
        设置模拟CDN基础URL（用于测试）
        
        Args:
            cdn_base: CDN基础URL
        """
        self.mock_cdn_base = cdn_base.rstrip('/')
        logger.info("Mock CDN base URL updated", cdn_base=self.mock_cdn_base)
