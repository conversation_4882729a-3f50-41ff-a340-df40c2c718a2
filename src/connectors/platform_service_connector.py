"""
平台服务连接器 - 物联网平台服务连接器
当前为模拟实现，返回预设的设备和空间数据
"""
import asyncio
import json
from typing import Dict, Any, List, Optional
from src.connectors.base_connector import BaseConnector, ConnectorError
from src.logger import get_logger
from src.config import settings
from src.models import ExtendParams

logger = get_logger(__name__)


class PlatformServiceConnector(BaseConnector):
    """
    平台服务连接器
    
    职责：
    1. 获取AI上下文信息（设备列表、空间信息）
    2. 执行设备控制操作
    3. 处理认证头透传
    
    当前为模拟实现，返回预设的测试数据
    """
    
    def __init__(self):
        super().__init__(
            base_url=settings.platform_service_url,
            timeout=settings.platform_service_timeout,
            max_retries=2
        )
        
        # 模拟设备数据
        self.mock_devices = [
            {
                "id": "light-01",
                "name": "会议室主照明",
                "type": "light",
                "location": "会议室A",
                "status": "OFF",
                "properties": {
                    "power": "OFF",
                    "brightness": 0,
                    "color": "#FFFFFF"
                },
                "capabilities": ["turnOn", "turnOff", "setBrightness"]
            },
            {
                "id": "ac-01", 
                "name": "会议室空调",
                "type": "airconditioner",
                "location": "会议室A",
                "status": "OFF",
                "properties": {
                    "power": "OFF",
                    "temperature": 26,
                    "mode": "auto",
                    "fanSpeed": "medium"
                },
                "capabilities": ["turnOn", "turnOff", "setTemperature", "setMode"]
            },
            {
                "id": "light-02",
                "name": "办公区照明",
                "type": "light", 
                "location": "办公区",
                "status": "ON",
                "properties": {
                    "power": "ON",
                    "brightness": 80,
                    "color": "#FFFFFF"
                },
                "capabilities": ["turnOn", "turnOff", "setBrightness"]
            },
            {
                "id": "sensor-01",
                "name": "温湿度传感器",
                "type": "sensor",
                "location": "会议室A", 
                "status": "ONLINE",
                "properties": {
                    "temperature": 24.5,
                    "humidity": 65,
                    "lastUpdate": "2023-10-27T10:30:00Z"
                },
                "capabilities": ["query"]
            }
        ]
        
        # 模拟空间数据
        self.mock_spaces = [
            {
                "id": "space-01",
                "name": "会议室A",
                "type": "meeting_room",
                "floor": 3,
                "deviceCount": 3
            },
            {
                "id": "space-02", 
                "name": "办公区",
                "type": "office",
                "floor": 3,
                "deviceCount": 5
            }
        ]
    
    async def get_ai_context(
        self,
        extend_params: ExtendParams,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取AI上下文信息
        
        Args:
            extend_params: 扩展参数，包含authorization和buildingId
            request_id: 请求ID
            
        Returns:
            Dict[str, Any]: 上下文信息，包含设备列表和空间信息
        """
        logger.info(
            "Getting AI context",
            building_id=extend_params.building_id,
            request_id=request_id
        )
        
        try:
            # 模拟API调用延迟
            await asyncio.sleep(0.2)
            
            # 构建请求头，透传认证信息
            headers = {
                "Authorization": extend_params.authorization,
                "Building-Id": extend_params.building_id
            }
            
            # 在真实实现中，这里会调用平台服务API
            # response = await self._make_request(
            #     method="GET",
            #     endpoint="/api/v1/ai/context",
            #     headers=headers,
            #     params={"buildingId": extend_params.building_id},
            #     request_id=request_id
            # )
            
            # 模拟实现：返回预设数据
            context_data = {
                "buildingId": extend_params.building_id,
                "devices": self.mock_devices.copy(),
                "spaces": self.mock_spaces.copy(),
                "timestamp": "2023-10-27T10:30:00Z",
                "permissions": {
                    "canControl": True,
                    "canQuery": True,
                    "restrictedDevices": []
                }
            }
            
            logger.info(
                "AI context retrieved",
                devices_count=len(context_data["devices"]),
                spaces_count=len(context_data["spaces"]),
                request_id=request_id
            )
            
            return context_data
            
        except ConnectorError:
            raise
        except Exception as e:
            logger.error(
                "Failed to get AI context",
                error=str(e),
                request_id=request_id
            )
            raise ConnectorError(
                f"Failed to get AI context: {str(e)}",
                error_code="CONTEXT_FETCH_ERROR",
                original_error=e
            )
    
    async def control_device(
        self,
        device_id: str,
        action: str,
        value: Any = None,
        extend_params: Optional[ExtendParams] = None,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        控制设备
        
        Args:
            device_id: 设备ID
            action: 操作类型
            value: 操作值
            extend_params: 扩展参数
            request_id: 请求ID
            
        Returns:
            Dict[str, Any]: 控制结果
        """
        logger.info(
            "Controlling device",
            device_id=device_id,
            action=action,
            value=value,
            request_id=request_id
        )
        
        try:
            # 模拟API调用延迟
            await asyncio.sleep(0.3)
            
            # 查找设备
            device = self._find_device(device_id)
            if not device:
                raise ConnectorError(
                    f"Device not found: {device_id}",
                    error_code="DEVICE_NOT_FOUND"
                )
            
            # 检查设备能力
            if action not in device.get("capabilities", []):
                raise ConnectorError(
                    f"Device {device_id} does not support action: {action}",
                    error_code="ACTION_NOT_SUPPORTED"
                )
            
            # 执行控制操作（模拟）
            result = self._execute_device_control(device, action, value)
            
            logger.info(
                "Device control completed",
                device_id=device_id,
                action=action,
                success=result["success"],
                request_id=request_id
            )
            
            return result
            
        except ConnectorError:
            raise
        except Exception as e:
            logger.error(
                "Device control failed",
                device_id=device_id,
                action=action,
                error=str(e),
                request_id=request_id
            )
            raise ConnectorError(
                f"Device control failed: {str(e)}",
                error_code="DEVICE_CONTROL_ERROR",
                original_error=e
            )
    
    async def execute_tool_calls(
        self,
        tool_calls: List[Dict[str, Any]],
        extend_params: ExtendParams,
        request_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        执行工具调用
        
        Args:
            tool_calls: LLM返回的工具调用列表
            extend_params: 扩展参数
            request_id: 请求ID
            
        Returns:
            List[Dict[str, Any]]: 工具执行结果列表
        """
        logger.info(
            "Executing tool calls",
            tool_calls_count=len(tool_calls),
            request_id=request_id
        )
        
        results = []
        
        for tool_call in tool_calls:
            try:
                function_name = tool_call.get("function", {}).get("name")
                arguments = tool_call.get("function", {}).get("arguments", {})
                
                if isinstance(arguments, str):
                    arguments = json.loads(arguments)
                
                if function_name == "controlDevice":
                    result = await self.control_device(
                        device_id=arguments.get("deviceId"),
                        action=arguments.get("action"),
                        value=arguments.get("value"),
                        extend_params=extend_params,
                        request_id=request_id
                    )
                elif function_name == "queryDeviceStatus":
                    result = await self.query_device_status(
                        device_id=arguments.get("deviceId"),
                        device_type=arguments.get("deviceType"),
                        extend_params=extend_params,
                        request_id=request_id
                    )
                else:
                    result = {
                        "success": False,
                        "error": f"Unknown function: {function_name}",
                        "errorCode": "UNKNOWN_FUNCTION"
                    }
                
                results.append({
                    "toolCallId": tool_call.get("id"),
                    "function": function_name,
                    "arguments": arguments,
                    **result
                })
                
            except Exception as e:
                logger.error(
                    "Tool call execution failed",
                    tool_call=tool_call,
                    error=str(e),
                    request_id=request_id
                )
                results.append({
                    "toolCallId": tool_call.get("id"),
                    "success": False,
                    "error": str(e),
                    "errorCode": "EXECUTION_ERROR"
                })
        
        logger.info(
            "Tool calls execution completed",
            results_count=len(results),
            success_count=sum(1 for r in results if r.get("success", False)),
            request_id=request_id
        )
        
        return results
    
    async def query_device_status(
        self,
        device_id: Optional[str] = None,
        device_type: Optional[str] = None,
        extend_params: Optional[ExtendParams] = None,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        查询设备状态
        
        Args:
            device_id: 设备ID，为空则查询所有设备
            device_type: 设备类型过滤
            extend_params: 扩展参数
            request_id: 请求ID
            
        Returns:
            Dict[str, Any]: 设备状态信息
        """
        logger.info(
            "Querying device status",
            device_id=device_id,
            device_type=device_type,
            request_id=request_id
        )
        
        try:
            # 模拟API调用延迟
            await asyncio.sleep(0.1)
            
            devices = self.mock_devices.copy()
            
            # 过滤设备
            if device_id:
                devices = [d for d in devices if d["id"] == device_id]
            if device_type:
                devices = [d for d in devices if d["type"] == device_type]
            
            result = {
                "success": True,
                "devices": devices,
                "timestamp": "2023-10-27T10:30:00Z",
                "totalCount": len(devices)
            }
            
            logger.info(
                "Device status query completed",
                devices_count=len(devices),
                request_id=request_id
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "Device status query failed",
                error=str(e),
                request_id=request_id
            )
            raise ConnectorError(
                f"Device status query failed: {str(e)}",
                error_code="QUERY_ERROR",
                original_error=e
            )
    
    def _find_device(self, device_id: str) -> Optional[Dict[str, Any]]:
        """查找设备"""
        for device in self.mock_devices:
            if device["id"] == device_id:
                return device
        return None
    
    def _execute_device_control(
        self, 
        device: Dict[str, Any], 
        action: str, 
        value: Any = None
    ) -> Dict[str, Any]:
        """执行设备控制（模拟）"""
        device_id = device["id"]
        device_name = device["name"]
        
        # 模拟控制操作
        if action == "turnOn":
            device["status"] = "ON"
            device["properties"]["power"] = "ON"
            if device["type"] == "light":
                device["properties"]["brightness"] = 100
        elif action == "turnOff":
            device["status"] = "OFF"
            device["properties"]["power"] = "OFF"
            if device["type"] == "light":
                device["properties"]["brightness"] = 0
        elif action == "setBrightness" and value is not None:
            device["properties"]["brightness"] = int(value)
            device["status"] = "ON" if int(value) > 0 else "OFF"
            device["properties"]["power"] = "ON" if int(value) > 0 else "OFF"
        elif action == "setTemperature" and value is not None:
            device["properties"]["temperature"] = float(value)
            device["status"] = "ON"
            device["properties"]["power"] = "ON"
        
        return {
            "success": True,
            "deviceId": device_id,
            "deviceName": device_name,
            "action": action,
            "value": value,
            "newState": device["properties"].copy(),
            "message": f"设备 {device_name} 操作成功",
            "timestamp": "2023-10-27T10:30:00Z"
        }
    
    async def health_check(self) -> bool:
        """健康检查"""
        logger.info("Platform service health check - mock implementation always healthy")
        return True
    
    def get_service_name(self) -> str:
        """获取服务名称"""
        return "Platform Service (Mock)"
