"""
阿里云TTS远程服务
基于阿里云语音合成REST API实现
"""
import http.client
import json
import asyncio
import ssl
import os
import time
import urllib.parse
from pathlib import Path
from typing import Optional, Dict, Any
from src.logger import get_logger
from src.config import settings

logger = get_logger(__name__)


class AliTTSRemoteService:
    """
    阿里云TTS远程服务
    
    支持：
    1. 文本到语音转换
    2. 异步调用
    3. 多种音频格式
    4. 完整的错误处理
    """
    
    def __init__(self, app_key: Optional[str] = None, token: Optional[str] = None):
        """
        初始化阿里云TTS服务
        
        Args:
            app_key: 阿里云应用Key
            token: 阿里云服务鉴权Token
        """
        self.app_key = app_key or getattr(settings, 'ali_tts_app_key', None)
        self.token = token or getattr(settings, 'ali_tts_token', None)
        self.host = 'nls-gateway-cn-shanghai.aliyuncs.com'
        self.base_url = '/stream/v1/tts'
        
        # 默认参数
        self.default_params = {
            'format': 'mp3',
            'sample_rate': 16000,
            'voice': 'xiaoyun',  # 发音人
            'volume': 50,        # 音量 0-100
            'speech_rate': 0,    # 语速 -500~500
            'pitch_rate': 0      # 语调 -500~500
        }
        
        # TTS文件存储目录
        self.tts_files_dir = Path("tts_files")
        self.tts_files_dir.mkdir(exist_ok=True)
        
        # HTTP服务器地址（用于返回音频文件URL）
        self.http_server_host = getattr(settings, 'tts_http_server_host', '************')
        
        logger.info(
            "AliTTSRemoteService initialized",
            app_key_configured=bool(self.app_key),
            token_configured=bool(self.token),
            host=self.host,
            tts_files_dir=str(self.tts_files_dir),
            http_server_host=self.http_server_host
        )
    
    async def text_to_speech(
        self, 
        text: str, 
        params: Optional[Dict[str, Any]] = None,
        use_post: bool = True
    ) -> Dict[str, Any]:
        """
        文本转语音
        
        Args:
            text: 要合成的文本
            params: TTS参数
            use_post: 是否使用POST请求（推荐）
            
        Returns:
            Dict[str, Any]: 合成结果
        """
        if not self.app_key or not self.token:
            raise ValueError("Ali TTS app_key and token must be configured")
        
        if not text or not text.strip():
            raise ValueError("Text is empty")
        
        # 合并参数
        request_params = {**self.default_params}
        if params:
            request_params.update(params)
        
        logger.info(
            f"Starting Ali TTS synthesis: {text[:50]}{'...' if len(text) > 50 else ''}",
            text_length=len(text),
            params=request_params,
            use_post=use_post
        )
        
        try:
            # 在线程池中执行同步HTTP请求
            if use_post:
                result = await asyncio.get_event_loop().run_in_executor(
                    None, 
                    self._sync_post_request, 
                    text, 
                    request_params
                )
            else:
                result = await asyncio.get_event_loop().run_in_executor(
                    None, 
                    self._sync_get_request, 
                    text, 
                    request_params
                )
            
            logger.info(
                f"Ali TTS synthesis completed: {result.get('message', '')}",
                success=result.get('success', False),
                audio_file=result.get('audio_file', ''),
                audio_url=result.get('audio_url', '')
            )
            
            return result
            
        except Exception as e:
            logger.error(
                "Ali TTS synthesis failed",
                error=str(e),
                text_length=len(text)
            )
            return {
                'success': False,
                'error': str(e),
                'audio_file': '',
                'audio_url': '',
                'message': f'TTS synthesis failed: {str(e)}'
            }
    
    def _sync_post_request(self, text: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        同步执行POST请求（推荐方式）
        
        Args:
            text: 要合成的文本
            params: TTS参数
            
        Returns:
            Dict[str, Any]: 合成结果
        """
        # 生成音频文件名
        timestamp = int(time.time() * 1000)
        text_hash = abs(hash(text)) % 100000
        audio_filename = f"tts_{text_hash}_{timestamp}.{params['format']}"
        audio_file_path = self.tts_files_dir / audio_filename
        
        # 设置请求头
        headers = {
            'Content-Type': 'application/json'
        }
        
        # 设置请求体
        body = {
            'appkey': self.app_key,
            'token': self.token,
            'text': text,
            'format': params['format'],
            'sample_rate': params['sample_rate']
        }
        
        # 添加可选参数
        if 'voice' in params:
            body['voice'] = params['voice']
        if 'volume' in params:
            body['volume'] = params['volume']
        if 'speech_rate' in params:
            body['speech_rate'] = params['speech_rate']
        if 'pitch_rate' in params:
            body['pitch_rate'] = params['pitch_rate']
        
        body_json = json.dumps(body)
        
        conn = None
        try:
            # 创建HTTPS连接
            conn = http.client.HTTPSConnection(
                self.host,
                timeout=30,
                context=ssl.create_default_context()
            )
            
            # 发送POST请求
            conn.request(
                method='POST',
                url=self.base_url,
                body=body_json,
                headers=headers
            )
            
            # 获取响应
            response = conn.getresponse()
            content_type = response.getheader('Content-Type')
            response_body = response.read()
            
            logger.debug(
                "Ali TTS HTTP response",
                status=response.status,
                reason=response.reason,
                content_type=content_type,
                response_size=len(response_body)
            )
            
            # 检查响应类型
            if content_type and 'audio/' in content_type:
                # 成功获取音频数据
                with open(audio_file_path, 'wb') as f:
                    f.write(response_body)
                
                # 生成HTTP访问URL
                audio_url = f"http://{self.http_server_host}/{audio_filename}"
                
                return {
                    'success': True,
                    'audio_file': str(audio_file_path),
                    'audio_url': audio_url,
                    'message': 'TTS synthesis successful',
                    'content_type': content_type,
                    'file_size': len(response_body)
                }
            else:
                # 请求失败，返回错误信息
                try:
                    error_info = json.loads(response_body.decode('utf-8'))
                    error_message = error_info.get('message', 'Unknown error')
                except:
                    error_message = response_body.decode('utf-8', errors='ignore')
                
                return {
                    'success': False,
                    'error': f"Ali TTS API error: {error_message}",
                    'audio_file': '',
                    'audio_url': '',
                    'message': f'TTS synthesis failed: {error_message}',
                    'status_code': response.status
                }
                
        except Exception as e:
            logger.error(
                "Ali TTS POST request failed",
                error=str(e),
                host=self.host,
                url=self.base_url
            )
            raise
        finally:
            if conn:
                conn.close()
    
    def _sync_get_request(self, text: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """
        同步执行GET请求（备用方式）
        
        Args:
            text: 要合成的文本
            params: TTS参数
            
        Returns:
            Dict[str, Any]: 合成结果
        """
        # 生成音频文件名
        timestamp = int(time.time() * 1000)
        text_hash = abs(hash(text)) % 100000
        audio_filename = f"tts_{text_hash}_{timestamp}.{params['format']}"
        audio_file_path = self.tts_files_dir / audio_filename
        
        # URL编码文本
        text_encoded = urllib.parse.quote_plus(text)
        text_encoded = text_encoded.replace("+", "%20")
        text_encoded = text_encoded.replace("*", "%2A")
        text_encoded = text_encoded.replace("%7E", "~")
        
        # 构建GET请求URL
        url_params = {
            'appkey': self.app_key,
            'token': self.token,
            'text': text_encoded,
            'format': params['format'],
            'sample_rate': str(params['sample_rate'])
        }
        
        # 添加可选参数
        if 'voice' in params:
            url_params['voice'] = params['voice']
        if 'volume' in params:
            url_params['volume'] = str(params['volume'])
        if 'speech_rate' in params:
            url_params['speech_rate'] = str(params['speech_rate'])
        if 'pitch_rate' in params:
            url_params['pitch_rate'] = str(params['pitch_rate'])
        
        # 构建完整URL
        query_string = urllib.parse.urlencode(url_params)
        request_url = f"{self.base_url}?{query_string}"
        
        conn = None
        try:
            # 创建HTTPS连接
            conn = http.client.HTTPSConnection(
                self.host,
                timeout=30,
                context=ssl.create_default_context()
            )
            
            # 发送GET请求
            conn.request(method='GET', url=request_url)
            
            # 获取响应
            response = conn.getresponse()
            content_type = response.getheader('Content-Type')
            response_body = response.read()
            
            logger.debug(
                "Ali TTS GET response",
                status=response.status,
                reason=response.reason,
                content_type=content_type,
                response_size=len(response_body)
            )
            
            # 检查响应类型
            if content_type and 'audio/' in content_type:
                # 成功获取音频数据
                with open(audio_file_path, 'wb') as f:
                    f.write(response_body)
                
                # 生成HTTP访问URL
                audio_url = f"http://{self.http_server_host}/{audio_filename}"
                
                return {
                    'success': True,
                    'audio_file': str(audio_file_path),
                    'audio_url': audio_url,
                    'message': 'TTS synthesis successful',
                    'content_type': content_type,
                    'file_size': len(response_body)
                }
            else:
                # 请求失败
                error_message = response_body.decode('utf-8', errors='ignore')
                return {
                    'success': False,
                    'error': f"Ali TTS GET request failed: {error_message}",
                    'audio_file': '',
                    'audio_url': '',
                    'message': f'TTS synthesis failed: {error_message}',
                    'status_code': response.status
                }
                
        except Exception as e:
            logger.error(
                "Ali TTS GET request failed",
                error=str(e),
                host=self.host,
                url=request_url
            )
            raise
        finally:
            if conn:
                conn.close()
    
    def is_configured(self) -> bool:
        """
        检查服务是否已正确配置
        
        Returns:
            bool: 是否已配置
        """
        return bool(self.app_key and self.token)
    
    def get_config_info(self) -> Dict[str, Any]:
        """
        获取配置信息（用于调试）
        
        Returns:
            Dict[str, Any]: 配置信息
        """
        return {
            'app_key_configured': bool(self.app_key),
            'token_configured': bool(self.token),
            'host': self.host,
            'base_url': self.base_url,
            'default_params': self.default_params,
            'tts_files_dir': str(self.tts_files_dir),
            'http_server_host': self.http_server_host
        }
