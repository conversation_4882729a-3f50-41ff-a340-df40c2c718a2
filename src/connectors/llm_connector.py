"""
LLM连接器 - 大语言模型服务连接器
支持Function Calling的真实LLM服务集成
"""
import json
import time
from typing import List, Dict, Any, Optional, Union
from src.connectors.base_connector import BaseConnector, ConnectorError
from src.logger import get_logger, log_llm_interaction
from src.config import settings

logger = get_logger(__name__)


class LLMConnector(BaseConnector):
    """
    LLM连接器
    
    职责：
    1. 构建完整的LLM请求（prompt, tools, messages）
    2. 调用LLM API进行推理
    3. 解析LLM响应，包括工具调用
    4. 支持两阶段调用：工具调用 + 最终回复生成
    """
    
    def __init__(self, tool_manager=None):
        super().__init__(
            base_url=settings.llm_api_url,
            timeout=settings.llm_timeout,
            max_retries=2
        )
        self.api_key = settings.llm_api_key
        self.model = settings.llm_model

        # 工具管理器（可选注入，用于获取动态工具定义）
        self.tool_manager = tool_manager

        # 设备控制工具定义（保持向后兼容）
        self.tools = self._build_tools_definition()

        # 系统提示词模板
        self.system_prompt = self._build_system_prompt()
    
    def _build_headers(self, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """构建请求头，包含API密钥"""
        headers = super()._build_headers(additional_headers)
        
        if self.api_key:
            # 根据不同的LLM服务调整认证方式
            if "dashscope" in self.base_url:
                # 阿里云通义千问
                headers["Authorization"] = f"Bearer {self.api_key}"
            elif "api.openai.com" in self.base_url:
                # OpenAI
                headers["Authorization"] = f"Bearer {self.api_key}"
            else:
                # 通用Bearer token
                headers["Authorization"] = f"Bearer {self.api_key}"
        
        return headers
    
    def _build_tools_definition(self) -> List[Dict[str, Any]]:
        """
        构建工具定义
        优先使用工具管理器的定义，否则使用默认定义
        """
        if self.tool_manager:
            try:
                return self.tool_manager.get_tool_definitions()
            except Exception as e:
                logger.warning(
                    "Failed to get tools from tool manager, using default",
                    error=str(e)
                )

        # 默认工具定义（保持向后兼容）
        return [
            {
                "type": "function",
                "function": {
                    "name": "controlDevice",
                    "description": "控制物联网设备，如开关灯、调节温度、控制空调等",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "deviceId": {
                                "type": "string",
                                "description": "设备ID"
                            },
                            "deviceName": {
                                "type": "string",
                                "description": "设备名称"
                            },
                            "action": {
                                "type": "string",
                                "description": "操作类型",
                                "enum": ["turnOn", "turnOff", "setBrightness", "setTemperature", "setMode"]
                            },
                            "value": {
                                "type": ["string", "number"],
                                "description": "操作值，如亮度值、温度值等"
                            },
                            "confirm": {
                                "type": "boolean",
                                "description": "是否需要用户确认，默认false"
                            }
                        },
                        "required": ["deviceId", "action"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "queryDeviceStatus",
                    "description": "查询设备状态信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "deviceId": {
                                "type": "string",
                                "description": "设备ID，如果为空则查询所有设备"
                            },
                            "deviceType": {
                                "type": "string",
                                "description": "设备类型过滤，如light、airconditioner等"
                            }
                        }
                    }
                }
            }
        ]

    def update_tools(self):
        """更新工具定义（当工具管理器中的工具发生变化时调用）"""
        self.tools = self._build_tools_definition()
        logger.info(
            "Tools updated",
            tool_count=len(self.tools),
            tools=[tool["function"]["name"] for tool in self.tools]
        )
    
    def _build_system_prompt(self) -> str:
        """构建系统提示词"""
        return """
你是一个智能楼宇管理助手，专门帮助用户控制和查询各种物联网设备。

你的能力包括：
1. 控制照明设备（开关、调节亮度）
2. 控制空调设备（开关、调节温度、模式切换）
3. 其他办公环境的设备（如窗帘、门禁等）
4. 查询设备状态和信息
5. 提供设备使用建议

重要规则：
1. 当用户要求控制设备时，必须使用controlDevice工具
2. 当用户查询设备状态时，必须使用queryDeviceStatus工具
3. 对于可能影响多个设备或重要操作，设置confirm=true要求用户确认
4. 始终以友好、专业的语气回复用户
5. 如果无法理解用户意图，请礼貌地要求澄清
6. 以专业洁简的语言回复用户,回复不超过两句话
7. 回复中不要包含任何控制字符（如换行符、制表符等）

当前可用设备信息将在用户消息中提供。"""

    async def generate_response(
        self,
        user_text: str,
        context_info: Dict[str, Any],
        conversation_history: Optional[List[Dict[str, str]]] = None,
        request_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成LLM响应
        
        Args:
            user_text: 用户输入文本
            context_info: 上下文信息（设备列表、空间信息等）
            conversation_history: 对话历史
            request_id: 请求ID
            
        Returns:
            Dict[str, Any]: LLM响应结果
        """
        start_time = time.time()
        
        logger.info(
            "Starting LLM inference",
            user_text=user_text,
            context_devices_count=len(context_info.get("devices", [])),
            history_length=len(conversation_history) if conversation_history else 0,
            request_id=request_id
        )
        
        try:
            # 构建消息列表
            messages = self._build_messages(user_text, context_info, conversation_history)
            
            # 构建请求体
            request_body = {
                "model": self.model,
                "messages": messages,
                "tools": self.tools,
                "tool_choice": "auto",
                "temperature": 0.1,
                "max_tokens": 2000
            }
            
            # 发送请求
            response = await self._make_request(
                method="POST",
                endpoint="/chat/completions" if "openai" in self.base_url else "",
                json_data=request_body,
                request_id=request_id
            )
            
            # 解析响应
            result = self._parse_llm_response(response, request_id)
            
            # 记录LLM交互日志
            processing_time = time.time() - start_time
            log_llm_interaction(
                request_id=request_id,
                model=self.model,
                prompt_tokens=response.get("usage", {}).get("prompt_tokens"),
                completion_tokens=response.get("usage", {}).get("completion_tokens"),
                response_time=processing_time
            )
            
            logger.info(
                "LLM inference completed",
                has_tool_calls=bool(result.get("tool_calls")),
                response_type=result.get("response_type"),
                processing_time=processing_time,
                request_id=request_id
            )
            
            return result
            
        except ConnectorError:
            raise
        except Exception as e:
            processing_time = time.time() - start_time
            log_llm_interaction(
                request_id=request_id,
                model=self.model,
                response_time=processing_time,
                error=str(e)
            )
            
            logger.error(
                "LLM inference failed",
                error=str(e),
                processing_time=processing_time,
                request_id=request_id
            )
            
            raise ConnectorError(
                f"LLM inference failed: {str(e)}",
                error_code="LLM_INFERENCE_ERROR",
                original_error=e
            )
    
    def _build_messages(
        self,
        user_text: str,
        context_info: Dict[str, Any],
        conversation_history: Optional[List[Dict[str, str]]] = None
    ) -> List[Dict[str, str]]:
        """构建消息列表"""
        messages = [
            {"role": "system", "content": self.system_prompt}
        ]
        
        # 添加对话历史
        if conversation_history:
            for turn in conversation_history[-10:]:  # 只保留最近10轮对话
                # 确保对话历史格式正确
                if isinstance(turn, dict):
                    if "user" in turn and turn["user"]:
                        messages.append({"role": "user", "content": turn["user"]})
                    if "assistant" in turn and turn["assistant"]:
                        messages.append({"role": "assistant", "content": turn["assistant"]})
        
        # 构建用户消息，包含上下文信息
        user_message = f"用户说：{user_text}\n\n"
        
        # 添加设备信息
        if context_info.get("devices"):
            user_message += "当前可用设备：\n"
            for device in context_info["devices"]:
                user_message += f"- {device.get('name', 'Unknown')} (ID: {device.get('id')}, 类型: {device.get('type')}, 状态: {device.get('status', 'Unknown')})\n"
        
        # 添加空间信息
        if context_info.get("spaces"):
            user_message += "\n当前空间信息：\n"
            for space in context_info["spaces"]:
                user_message += f"- {space.get('name', 'Unknown')} (ID: {space.get('id')})\n"
        
        messages.append({"role": "user", "content": user_message})
        
        return messages
    
    def _parse_llm_response(self, response: Dict[str, Any], request_id: Optional[str] = None) -> Dict[str, Any]:
        """解析LLM响应"""
        try:
            choices = response.get("choices", [])
            if not choices:
                raise ConnectorError("No choices in LLM response", error_code="INVALID_RESPONSE")
            
            message = choices[0].get("message", {})
            content = message.get("content", "")
            tool_calls = message.get("tool_calls", [])
            
            result = {
                "content": content,
                "tool_calls": tool_calls,
                "response_type": "tool_call" if tool_calls else "text",
                "usage": response.get("usage", {}),
                "model": response.get("model", self.model)
            }
            
            return result
            
        except Exception as e:
            logger.error(
                "Failed to parse LLM response",
                response=response,
                error=str(e),
                request_id=request_id
            )
            raise ConnectorError(
                f"Failed to parse LLM response: {str(e)}",
                error_code="RESPONSE_PARSE_ERROR",
                original_error=e
            )
    
    async def generate_final_response(
        self,
        original_request: str,
        tool_results: List[Dict[str, Any]],
        request_id: Optional[str] = None
    ) -> str:
        """
        生成最终回复
        
        Args:
            original_request: 原始用户请求
            tool_results: 工具执行结果
            request_id: 请求ID
            
        Returns:
            str: 最终回复文本
        """
        logger.info(
            "Generating final response",
            original_request=original_request,
            tool_results_count=len(tool_results),
            request_id=request_id
        )
        
        # 构建包含工具执行结果的消息
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": original_request},
            {
                "role": "assistant", 
                "content": f"我已经执行了相关操作，结果如下：\n{json.dumps(tool_results, ensure_ascii=False, indent=2)}\n\n请根据执行结果生成一个友好的回复给用户。"
            }
        ]
        
        try:
            request_body = {
                "model": self.model,
                "messages": messages,
                "temperature": 0.3,
                "max_tokens": 500
            }
            
            response = await self._make_request(
                method="POST",
                endpoint="/chat/completions" if "openai" in self.base_url else "",
                json_data=request_body,
                request_id=request_id
            )
            
            choices = response.get("choices", [])
            if choices:
                return choices[0].get("message", {}).get("content", "操作已完成")
            else:
                return "操作已完成"
                
        except Exception as e:
            logger.error(
                "Failed to generate final response",
                error=str(e),
                request_id=request_id
            )
            # 如果生成最终回复失败，返回默认回复
            return "操作已完成，请检查设备状态"
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            # 发送简单的测试请求
            test_messages = [
                {"role": "user", "content": "Hello"}
            ]
            
            request_body = {
                "model": self.model,
                "messages": test_messages,
                "max_tokens": 10
            }
            
            await self._make_request(
                method="POST",
                endpoint="/chat/completions" if "openai" in self.base_url else "",
                json_data=request_body
            )
            
            return True
            
        except Exception as e:
            logger.error("LLM health check failed", error=str(e))
            return False
    
    def get_service_name(self) -> str:
        """获取服务名称"""
        return f"LLM Service ({self.model})"
