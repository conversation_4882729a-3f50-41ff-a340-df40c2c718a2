"""
对话上下文管理器
使用Redis存储和管理对话上下文数据
"""
import json
import time
from typing import Dict, Any, List, Optional
import redis.asyncio as redis
from src.logger import get_logger
from src.config import settings

logger = get_logger(__name__)


class ContextManager:
    """
    对话上下文管理器
    
    职责：
    1. 存储和检索对话历史
    2. 管理会话状态
    3. 处理上下文过期
    4. 提供上下文数据给LLM
    """
    
    def __init__(self):
        self.redis_client: Optional[redis.Redis] = None
        self.expire_seconds = settings.context_expire_seconds
        self.max_turns = settings.max_context_turns
        
        # Redis键前缀
        self.session_prefix = "voice_session:"
        self.conversation_prefix = "voice_conversation:"
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._ensure_connection()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()
    
    async def _ensure_connection(self):
        """确保Redis连接已建立"""
        if self.redis_client is None:
            try:
                self.redis_client = redis.Redis(
                    host=settings.redis_host,
                    port=settings.redis_port,
                    password=settings.redis_password,
                    db=settings.redis_db,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5
                )
                
                # 测试连接
                await self.redis_client.ping()
                
                logger.info(
                    "Redis connection established",
                    host=settings.redis_host,
                    port=settings.redis_port,
                    db=settings.redis_db
                )
                
            except Exception as e:
                logger.error(
                    "Failed to connect to Redis",
                    error=str(e),
                    host=settings.redis_host,
                    port=settings.redis_port
                )
                raise
    
    async def close(self):
        """关闭Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
            self.redis_client = None
            logger.info("Redis connection closed")
    
    async def get_conversation_history(
        self, 
        session_id: str, 
        max_turns: Optional[int] = None
    ) -> List[Dict[str, str]]:
        """
        获取对话历史
        
        Args:
            session_id: 会话ID
            max_turns: 最大对话轮数，默认使用配置值
            
        Returns:
            List[Dict[str, str]]: 对话历史列表
        """
        await self._ensure_connection()
        
        try:
            conversation_key = f"{self.conversation_prefix}{session_id}"
            
            # 获取对话历史
            history_data = await self.redis_client.get(conversation_key)
            
            if not history_data:
                logger.info("No conversation history found", session_id=session_id)
                return []
            
            history = json.loads(history_data)
            conversations = history.get("conversations", [])
            
            # 限制返回的对话轮数
            max_turns = max_turns or self.max_turns
            if len(conversations) > max_turns:
                conversations = conversations[-max_turns:]
            
            logger.info(
                "Conversation history retrieved",
                session_id=session_id,
                turns_count=len(conversations)
            )
            
            return conversations
            
        except Exception as e:
            logger.error(
                "Failed to get conversation history",
                session_id=session_id,
                error=str(e)
            )
            return []
    
    async def add_conversation_turn(
        self,
        session_id: str,
        user_message: str,
        assistant_message: str,
        metadata: Optional[Dict[str, Any]] = None
    ):
        """
        添加对话轮次
        
        Args:
            session_id: 会话ID
            user_message: 用户消息
            assistant_message: 助手回复
            metadata: 额外的元数据
        """
        await self._ensure_connection()
        
        try:
            conversation_key = f"{self.conversation_prefix}{session_id}"
            
            # 获取现有历史
            history_data = await self.redis_client.get(conversation_key)
            
            if history_data:
                history = json.loads(history_data)
            else:
                history = {
                    "sessionId": session_id,
                    "createdAt": int(time.time() * 1000),
                    "conversations": []
                }
            
            # 添加新的对话轮次
            turn = {
                "timestamp": int(time.time() * 1000),
                "user": user_message,
                "assistant": assistant_message
            }
            
            if metadata:
                turn["metadata"] = metadata
            
            history["conversations"].append(turn)
            history["updatedAt"] = int(time.time() * 1000)
            
            # 限制历史长度
            if len(history["conversations"]) > self.max_turns:
                history["conversations"] = history["conversations"][-self.max_turns:]
            
            # 保存到Redis
            await self.redis_client.setex(
                conversation_key,
                self.expire_seconds,
                json.dumps(history, ensure_ascii=False)
            )
            
            logger.info(
                "Conversation turn added",
                session_id=session_id,
                total_turns=len(history["conversations"])
            )
            
        except Exception as e:
            logger.error(
                "Failed to add conversation turn",
                session_id=session_id,
                error=str(e)
            )
    
    async def get_session_context(self, session_id: str) -> Dict[str, Any]:
        """
        获取会话上下文
        
        Args:
            session_id: 会话ID
            
        Returns:
            Dict[str, Any]: 会话上下文数据
        """
        await self._ensure_connection()
        
        try:
            session_key = f"{self.session_prefix}{session_id}"
            
            # 获取会话数据
            session_data = await self.redis_client.get(session_key)
            
            if not session_data:
                # 创建新会话
                context = {
                    "sessionId": session_id,
                    "createdAt": int(time.time() * 1000),
                    "lastActiveAt": int(time.time() * 1000),
                    "turnCount": 0,
                    "preferences": {},
                    "state": "active"
                }
                
                await self.redis_client.setex(
                    session_key,
                    self.expire_seconds,
                    json.dumps(context, ensure_ascii=False)
                )
                
                logger.info("New session context created", session_id=session_id)
                return context
            
            context = json.loads(session_data)
            
            # 更新最后活跃时间
            context["lastActiveAt"] = int(time.time() * 1000)
            await self.redis_client.setex(
                session_key,
                self.expire_seconds,
                json.dumps(context, ensure_ascii=False)
            )
            
            logger.info("Session context retrieved", session_id=session_id)
            return context
            
        except Exception as e:
            logger.error(
                "Failed to get session context",
                session_id=session_id,
                error=str(e)
            )
            # 返回默认上下文
            return {
                "sessionId": session_id,
                "createdAt": int(time.time() * 1000),
                "lastActiveAt": int(time.time() * 1000),
                "turnCount": 0,
                "preferences": {},
                "state": "active"
            }
    
    async def update_session_context(
        self, 
        session_id: str, 
        updates: Dict[str, Any]
    ):
        """
        更新会话上下文
        
        Args:
            session_id: 会话ID
            updates: 要更新的数据
        """
        await self._ensure_connection()
        
        try:
            session_key = f"{self.session_prefix}{session_id}"
            
            # 获取现有上下文
            context = await self.get_session_context(session_id)
            
            # 更新数据
            context.update(updates)
            context["updatedAt"] = int(time.time() * 1000)
            
            # 保存到Redis
            await self.redis_client.setex(
                session_key,
                self.expire_seconds,
                json.dumps(context, ensure_ascii=False)
            )
            
            logger.info(
                "Session context updated",
                session_id=session_id,
                updates=list(updates.keys())
            )
            
        except Exception as e:
            logger.error(
                "Failed to update session context",
                session_id=session_id,
                error=str(e)
            )
    
    async def clear_session(self, session_id: str):
        """
        清除会话数据
        
        Args:
            session_id: 会话ID
        """
        await self._ensure_connection()
        
        try:
            session_key = f"{self.session_prefix}{session_id}"
            conversation_key = f"{self.conversation_prefix}{session_id}"
            
            # 删除会话和对话数据
            await self.redis_client.delete(session_key, conversation_key)
            
            logger.info("Session cleared", session_id=session_id)
            
        except Exception as e:
            logger.error(
                "Failed to clear session",
                session_id=session_id,
                error=str(e)
            )
    
    async def health_check(self) -> bool:
        """
        健康检查
        
        Returns:
            bool: Redis连接是否正常
        """
        try:
            await self._ensure_connection()
            await self.redis_client.ping()
            return True
        except Exception as e:
            logger.error("Redis health check failed", error=str(e))
            return False
