"""
日志配置模块
使用structlog提供结构化日志
"""
import sys
import logging
import structlog
from typing import Any, Dict
from src.config import settings


def configure_logging() -> None:
    """配置日志系统"""

    # 配置标准库logging
    logging.basicConfig(
        level=getattr(logging, settings.log_level.upper()),
        format="%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s",
        stream=sys.stdout
    )

    # 配置structlog
    if settings.log_format == "json":
        # JSON格式日志，适合生产环境
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                # 添加文件名和行号信息
                _add_caller_info,
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    else:
        # 文本格式日志，适合开发环境
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                # 添加文件名和行号信息
                _add_caller_info,
                structlog.dev.ConsoleRenderer(colors=True)
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )


def _add_caller_info(logger, method_name, event_dict):
    """添加调用者信息（文件名和行号）"""
    import inspect

    # 获取调用栈
    frame = inspect.currentframe()
    try:
        # 向上查找调用者
        caller_frame = frame
        for _ in range(10):  # 最多查找10层
            caller_frame = caller_frame.f_back
            if caller_frame is None:
                break

            filename = caller_frame.f_code.co_filename
            # 跳过structlog内部调用
            if 'structlog' not in filename and 'logging' not in filename:
                event_dict['file'] = filename.split('/')[-1].split('\\')[-1]
                event_dict['line'] = caller_frame.f_lineno
                event_dict['function'] = caller_frame.f_code.co_name
                break
    finally:
        del frame

    return event_dict


def get_logger(name: str = __name__) -> structlog.BoundLogger:
    """获取日志记录器"""
    return structlog.get_logger(name)


def log_request_response(
    request_id: str,
    method: str,
    url: str,
    status_code: int = None,
    response_time: float = None,
    error: str = None,
    **kwargs
) -> None:
    """记录请求响应日志"""
    logger = get_logger("request")
    
    log_data = {
        "request_id": request_id,
        "method": method,
        "url": url,
        **kwargs
    }
    
    if status_code:
        log_data["status_code"] = status_code
    if response_time:
        log_data["response_time"] = response_time
    if error:
        log_data["error"] = error
        logger.error("Request failed", **log_data)
    else:
        logger.info("Request completed", **log_data)


def log_llm_interaction(
    request_id: str,
    model: str,
    prompt_tokens: int = None,
    completion_tokens: int = None,
    response_time: float = None,
    error: str = None,
    **kwargs
) -> None:
    """记录LLM交互日志"""
    logger = get_logger("llm")
    
    log_data = {
        "request_id": request_id,
        "model": model,
        **kwargs
    }
    
    if prompt_tokens:
        log_data["prompt_tokens"] = prompt_tokens
    if completion_tokens:
        log_data["completion_tokens"] = completion_tokens
    if response_time:
        log_data["response_time"] = response_time
    if error:
        log_data["error"] = error
        logger.error("LLM interaction failed", **log_data)
    else:
        logger.info("LLM interaction completed", **log_data)
