"""
交互编排服务层 - 语音交互服务
职责：核心业务逻辑，编排所有下游服务的调用
"""
import time
from typing import Optional, Dict, Any, List

from src.logger import get_logger
from src.models import (
    VoiceInteractRequest,
    VoiceInteractResponse,
    VoiceInteractResponseData,
    InteractionType,
    UIComponent,
    DeviceUpdate,
    ContextData
)
from src.connectors import (
    ASRConnector,
    LLMConnector,
    TTSConnector,
    PlatformServiceConnector
)
from src.storage import ContextManager
from src.tools import ToolManager

logger = get_logger(__name__)


class VoiceInteractionService:
    """
    语音交互编排服务
    
    按照技术方案中定义的工作流程：
    1. ASR语音转文本
    2. 获取历史对话上下文和设备信息
    3. LLM推理和工具调用
    4. 执行设备控制或准备确认
    5. 生成最终回复
    6. TTS语音合成
    7. 组装响应数据
    """
    
    def __init__(self):
        # 初始化平台连接器（共享给工具管理器）
        self.platform_connector = PlatformServiceConnector()

        # 初始化工具管理器
        self.tool_manager = ToolManager(self.platform_connector)

        # 初始化其他连接器
        self.asr_connector = ASRConnector()
        self.llm_connector = LLMConnector(self.tool_manager)  # 注入工具管理器
        self.tts_connector = TTSConnector()
        self.context_manager = ContextManager()

        logger.info(
            "VoiceInteractionService initialized with all connectors",
            available_tools=self.tool_manager.get_available_tools()
        )
    
    async def process_voice_interaction(
        self, 
        request: VoiceInteractRequest
    ) -> VoiceInteractResponse:
        """
        处理语音交互请求
        
        实现完整的语音交互工作流程
        """
        start_time = time.time()
        request_id = request.request_id
        
        logger.info(
            "Starting voice interaction processing",
            request_id=request_id,
            building_id=request.extend.building_id
        )
        
        try:
            # 1. ASR语音转文本
            user_text = await self._process_asr(request.audio_data, request_id)
            
            # 2. 获取上下文信息
            context_info = await self._get_context_info(request, request_id)
            
            # 3. LLM推理和工具调用
            llm_result = await self._process_llm_inference(
                user_text, context_info, request_id
            )
            
            # 4. 执行设备控制或处理其他操作
            execution_result = await self._execute_operations(
                llm_result, request, request_id
            )
            
            # 5. 生成最终回复
            final_response = await self._generate_final_response(
                llm_result, execution_result, user_text, request_id
            )
            
            # 6. TTS语音合成（可选）
            audio_url = await self._process_tts(final_response, request_id)
            
            # 7. 组装响应数据
            response_data = self._build_response_data(
                user_text, final_response, audio_url, llm_result, execution_result
            )

            # 8. 保存对话历史
            try:
                session_id = context_info.get("sessionId")
                if session_id:
                    await self.context_manager.add_conversation_turn(
                        session_id=session_id,
                        user_message=user_text,
                        assistant_message=final_response,
                        metadata={
                            "requestId": request_id,
                            "hasToolCalls": bool(llm_result.get("tool_calls")),
                            "operationsCount": execution_result.get("successful_operations", 0),
                            "processingTime": int((time.time() - start_time) * 1000)
                        }
                    )
            except Exception as e:
                logger.warning(
                    "Failed to save conversation history",
                    error=str(e),
                    request_id=request_id
                )

            cost = int((time.time() - start_time) * 1000)

            response = VoiceInteractResponse(
                requestId=request_id,
                success=True,
                timestamp=int(time.time() * 1000),
                cost=cost,
                data=response_data,
                code="0",
                msg="处理成功"
            )

            logger.info(
                "Voice interaction processing completed",
                request_id=request_id,
                cost=cost,
                interaction_type=response_data.interaction_type
            )

            return response
            
        except Exception as e:
            cost = int((time.time() - start_time) * 1000)
            
            logger.error(
                "Voice interaction processing failed",
                request_id=request_id,
                error=str(e),
                cost=cost
            )
            
            raise
    
    async def _process_asr(self, audio_data: str, request_id: str) -> str:
        """步骤1：ASR语音转文本"""
        logger.info("Processing ASR", request_id=request_id)

        try:
            # 调用ASR Connector
            result = await self.asr_connector.speech_to_text(
                audio_data=audio_data,
                request_id=request_id
            )

            logger.info(
                "ASR processing completed",
                result=result,
                request_id=request_id
            )

            return result

        except Exception as e:
            logger.error(
                "ASR processing failed",
                error=str(e),
                request_id=request_id
            )
            # 如果ASR失败，返回默认文本以便继续流程
            return "语音识别失败，请重试"
    
    async def _get_context_info(self, request: VoiceInteractRequest, request_id: str) -> Dict[str, Any]:
        """步骤2：获取上下文信息"""
        logger.info("Getting context info", request_id=request_id)

        try:
            # 并行获取历史对话上下文和设备信息
            import asyncio

            # 生成会话ID（基于buildingId和authorization的组合）
            session_id = f"session_{request.extend.building_id}_{hash(request.extend.authorization) % 10000}"

            # 并行获取上下文信息
            context_task = self.context_manager.get_conversation_history(session_id)
            device_info_task = self.platform_connector.get_ai_context(
                extend_params=request.extend,
                request_id=request_id
            )

            # 等待所有任务完成
            conversation_history, device_info = await asyncio.gather(
                context_task, device_info_task, return_exceptions=True
            )

            # 处理异常结果
            if isinstance(conversation_history, Exception):
                logger.warning(
                    "Failed to get conversation history",
                    error=str(conversation_history),
                    request_id=request_id
                )
                conversation_history = []

            if isinstance(device_info, Exception):
                logger.warning(
                    "Failed to get device info",
                    error=str(device_info),
                    request_id=request_id
                )
                device_info = {"devices": [], "spaces": [], "permissions": {}}

            # 构建完整的上下文信息
            context_info = {
                "conversation_history": conversation_history,
                "devices": device_info.get("devices", []),
                "spaces": device_info.get("spaces", []),
                "permissions": device_info.get("permissions", {}),
                "buildingId": request.extend.building_id,
                "sessionId": session_id
            }

            logger.info(
                "Context info retrieved",
                devices_count=len(context_info["devices"]),
                spaces_count=len(context_info["spaces"]),
                request_id=request_id
            )

            return context_info

        except Exception as e:
            logger.error(
                "Failed to get context info",
                error=str(e),
                request_id=request_id
            )
            # 返回空的上下文信息以便继续流程
            return {
                "conversation_history": [],
                "devices": [],
                "spaces": [],
                "permissions": {"canControl": False, "canQuery": True},
                "buildingId": request.extend.building_id
            }
    
    async def _process_llm_inference(
        self,
        user_text: str,
        context_info: Dict[str, Any],
        request_id: str
    ) -> Dict[str, Any]:
        """步骤3：LLM推理和工具调用"""
        logger.info("Processing LLM inference", request_id=request_id)

        try:
            # 调用LLM Connector
            result = await self.llm_connector.generate_response(
                user_text=user_text,
                context_info=context_info,
                conversation_history=context_info.get("conversation_history", []),
                request_id=request_id
            )

            logger.info(
                "LLM inference completed",
                response_type=result.get("response_type"),
                has_tool_calls=bool(result.get("tool_calls")),
                request_id=request_id
            )

            return result

        except Exception as e:
            logger.error(
                "LLM inference failed",
                error=str(e),
                request_id=request_id
            )
            # 返回默认的文本回复
            return {
                "content": "抱歉，我现在无法处理您的请求，请稍后再试。",
                "tool_calls": [],
                "response_type": "text",
                "usage": {},
                "model": "unknown"
            }
    
    async def _execute_operations(
        self,
        llm_result: Dict[str, Any],
        request: VoiceInteractRequest,
        request_id: str
    ) -> Dict[str, Any]:
        """步骤4：执行设备控制或其他操作"""
        logger.info("Executing operations", request_id=request_id)

        try:
            # 检查是否有工具调用
            tool_calls = llm_result.get("tool_calls", [])

            if tool_calls:
                # 构建执行上下文
                execution_context = {
                    "authorization": request.extend.authorization,
                    "buildingId": request.extend.building_id,
                    "permissions": {
                        "canControl": True,  # TODO: 从实际权限系统获取
                        "canQuery": True
                    }
                }

                # 使用工具管理器执行工具调用
                tool_results = await self.tool_manager.execute_tool_calls(
                    tool_calls=tool_calls,
                    context=execution_context,
                    max_concurrent=3  # 限制并发数
                )

                # 转换为平台服务格式的结果
                results = []
                for tool_result in tool_results:
                    result_dict = tool_result.to_dict()
                    # 保持与原有格式的兼容性
                    results.append({
                        "success": result_dict["success"],
                        "deviceId": result_dict.get("data", {}).get("deviceId"),
                        "deviceName": result_dict.get("data", {}).get("deviceName"),
                        "action": result_dict.get("data", {}).get("action"),
                        "message": result_dict["message"],
                        "newState": result_dict.get("data", {}).get("newState", {}),
                        "errorCode": result_dict.get("error_code"),
                        "executionTime": result_dict.get("execution_time_ms")
                    })

                # 统计执行结果
                success_count = sum(1 for r in results if r.get("success", False))
                total_count = len(results)

                execution_result = {
                    "success": success_count > 0,
                    "total_operations": total_count,
                    "successful_operations": success_count,
                    "failed_operations": total_count - success_count,
                    "results": results
                }

                logger.info(
                    "Operations executed",
                    total=total_count,
                    successful=success_count,
                    failed=total_count - success_count,
                    request_id=request_id
                )

                return execution_result
            else:
                # 没有工具调用，返回文本回复结果
                return {
                    "success": True,
                    "total_operations": 0,
                    "successful_operations": 0,
                    "failed_operations": 0,
                    "results": [],
                    "response_type": "text_only"
                }

        except Exception as e:
            logger.error(
                "Operations execution failed",
                error=str(e),
                request_id=request_id
            )
            return {
                "success": False,
                "total_operations": 0,
                "successful_operations": 0,
                "failed_operations": 1,
                "results": [],
                "error": str(e)
            }
    
    async def _generate_final_response(
        self,
        llm_result: Dict[str, Any],
        execution_result: Dict[str, Any],
        user_text: str,
        request_id: str
    ) -> str:
        """步骤5：生成最终回复"""
        logger.info("Generating final response", request_id=request_id)

        try:
            # 如果有工具调用执行结果，让LLM生成基于结果的回复
            if execution_result.get("results"):
                final_response = await self.llm_connector.generate_final_response(
                    original_request=user_text,
                    tool_results=execution_result["results"],
                    request_id=request_id
                )
            else:
                # 没有工具调用，直接使用LLM的原始回复
                final_response = llm_result.get("content", "我已经收到您的消息。")

            logger.info(
                "Final response generated",
                response_length=len(final_response),
                request_id=request_id
            )

            return final_response

        except Exception as e:
            logger.error(
                "Failed to generate final response",
                error=str(e),
                request_id=request_id
            )
            # 返回默认回复
            if execution_result.get("success"):
                return "操作已完成，请检查设备状态。"
            else:
                return "抱歉，操作执行失败，请稍后重试。"
    
    async def _process_tts(self, response_text: str, request_id: str) -> Optional[str]:
        """步骤6：TTS语音合成"""
        logger.info("Processing TTS", request_id=request_id)

        try:
            # 调用TTS Connector生成语音
            audio_url = await self.tts_connector.text_to_speech(
                text=response_text,
                voice_config="default",
                output_format="mp3",
                return_base64=False,  # 返回URL模式
                request_id=request_id
            )

            logger.info(
                "TTS processing completed",
                audio_url=audio_url,
                request_id=request_id
            )

            return audio_url

        except Exception as e:
            logger.error(
                "TTS processing failed",
                error=str(e),
                request_id=request_id
            )
            # TTS失败不影响整体流程，返回None
            return None
    
    def _build_response_data(
        self,
        user_text: str,
        response_text: str,
        audio_url: Optional[str],
        llm_result: Dict[str, Any],
        execution_result: Dict[str, Any]
    ) -> VoiceInteractResponseData:
        """步骤7：组装响应数据"""

        # 根据执行结果确定交互类型
        interaction_type = self._determine_interaction_type(llm_result, execution_result)

        # 构建UI组件
        ui_component = self._build_ui_component(
            interaction_type, response_text, execution_result
        )

        # 构建设备状态更新数据
        device_updates = self._build_device_updates(execution_result)

        # 构建上下文数据
        context_data = self._build_context_data(execution_result)

        return VoiceInteractResponseData(
            text=user_text,
            responseText=response_text,
            audioUrl=audio_url,
            interactionType=interaction_type,
            ui=ui_component,
            deviceUpdates=device_updates,
            contextData=context_data
        )

    def _determine_interaction_type(
        self,
        llm_result: Dict[str, Any],
        execution_result: Dict[str, Any]
    ) -> InteractionType:
        """确定交互类型"""

        # 检查是否有工具调用
        if llm_result.get("tool_calls"):
            # 检查执行结果
            if execution_result.get("success", False):
                return InteractionType.RESULT
            else:
                return InteractionType.ERROR
        else:
            # 纯文本对话
            return InteractionType.CONVERSATION

    def _build_ui_component(
        self,
        interaction_type: InteractionType,
        response_text: str,
        execution_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """构建UI组件"""

        if interaction_type == InteractionType.RESULT:
            return {
                "type": "ResultCard",
                "title": "操作成功",
                "content": response_text,
                "status": "success",
                "data": {
                    "operationsCount": execution_result.get("successful_operations", 0),
                    "timestamp": int(time.time() * 1000)
                }
            }
        elif interaction_type == InteractionType.ERROR:
            return {
                "type": "ResultCard",
                "title": "操作失败",
                "content": response_text,
                "status": "error",
                "data": {
                    "errorCount": execution_result.get("failed_operations", 0),
                    "timestamp": int(time.time() * 1000)
                }
            }
        else:
            # 普通对话
            return {
                "type": "ConversationCard",
                "title": "智能助手",
                "content": response_text,
                "status": "info"
            }

    def _build_device_updates(self, execution_result: Dict[str, Any]) -> Optional[List[DeviceUpdate]]:
        """构建设备状态更新数据"""

        device_updates = []
        results = execution_result.get("results", [])

        for result in results:
            if result.get("success") and result.get("newState"):
                device_update = DeviceUpdate(
                    deviceId=result.get("deviceId"),
                    deviceName=result.get("deviceName"),
                    properties=result.get("newState", {}),
                    timestamp=int(time.time() * 1000)
                )
                device_updates.append(device_update)

        return device_updates if device_updates else None

    def _build_context_data(self, execution_result: Dict[str, Any]) -> Optional[ContextData]:
        """构建上下文数据"""

        # 构建最后操作信息
        last_operation = None
        results = execution_result.get("results", [])

        if results:
            last_result = results[-1]  # 取最后一个操作结果
            last_operation = {
                "type": "device_control",
                "deviceId": last_result.get("deviceId"),
                "action": last_result.get("action"),
                "success": last_result.get("success", False),
                "timestamp": int(time.time() * 1000)
            }

        # TODO: 从实际会话中获取sessionId和conversationId
        context_data = ContextData(
            sessionId="session-temp-001",  # 临时会话ID
            conversationId=f"conv-{int(time.time())}",
            lastOperation=last_operation
        )

        return context_data

    async def close(self):
        """关闭所有连接器"""
        logger.info("Closing VoiceInteractionService and all connectors")

        try:
            await self.asr_connector.close()
            await self.llm_connector.close()
            await self.tts_connector.close()
            await self.platform_connector.close()
            await self.context_manager.close()
            await self.tool_manager.close()

            logger.info("All connectors and tool manager closed successfully")
        except Exception as e:
            logger.error("Error closing connectors", error=str(e))
