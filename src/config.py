"""
配置管理模块
支持从环境变量和配置文件加载配置
"""
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    app_name: str = Field(default="InxVision Voice Service", description="应用名称")
    app_version: str = Field(default="0.1.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    
    # 服务配置
    host: str = Field(default="0.0.0.0", description="服务监听地址")
    port: int = Field(default=6090, description="服务监听端口")
    
    # Redis配置
    redis_host: str = Field(default="***********", description="Redis主机地址")
    redis_port: int = Field(default=6379, description="Redis端口")
    redis_password: Optional[str] = Field(default="inxvision", description="Redis密码")
    redis_db: int = Field(default=0, description="Redis数据库编号")
    
    # 对话上下文配置
    context_expire_seconds: int = Field(default=1800, description="对话上下文过期时间(秒)")
    max_context_turns: int = Field(default=10, description="最大对话轮数")
    
    # LLM配置
    llm_api_url: str = Field(default="https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions", description="LLM API地址")
    llm_api_key: str = Field(default="sk-bd5646b4accc4fa8b968064e2a1c65f0", description="LLM API密钥")
    llm_model: str = Field(default="qwen-long", description="LLM模型名称")
    llm_timeout: int = Field(default=30, description="LLM请求超时时间(秒)")
    
    # ASR配置 (模拟阶段暂不使用)
    asr_api_url: str = Field(default="", description="ASR API地址")
    asr_api_key: str = Field(default="", description="ASR API密钥")

    # 阿里云ASR配置
    ali_asr_app_key: str = Field(default="4WpCDor2ru5yB6GZ", description="阿里云ASR应用Key")
    ali_asr_token: str = Field(default="d0e1ceb3240341c1ad1d9276a4795549", description="阿里云ASR服务鉴权Token")
    ali_asr_enabled: bool = Field(default=True, description="是否启用阿里云ASR服务")

    # 阿里云TTS配置
    ali_tts_app_key: str = Field(default="4WpCDor2ru5yB6GZ", description="阿里云TTS应用Key")
    ali_tts_token: str = Field(default="d0e1ceb3240341c1ad1d9276a4795549", description="阿里云TTS服务鉴权Token")
    ali_tts_enabled: bool = Field(default=True, description="是否启用阿里云TTS服务")
    tts_http_server_host: str = Field(default="************", description="TTS音频文件HTTP服务器地址")
    
    # TTS配置 (模拟阶段暂不使用)
    tts_api_url: str = Field(default="", description="TTS API地址")
    tts_api_key: str = Field(default="", description="TTS API密钥")
    
    # Platform Service配置
    platform_service_url: str = Field(default="http://localhost:8001", description="平台服务地址")
    platform_service_timeout: int = Field(default=10, description="平台服务请求超时时间(秒)")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(default="json", description="日志格式: json|text")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# 全局配置实例
settings = Settings()
