# 物联网机电管理之人机语音交互技术方案

## 1. 引言

### 1.1. 项目背景

本项目旨在为现有的成熟物联网机电管理平台增加人机语音交互能力。平台已具备完善的设备接入、数据采集、设备控制及Web端和屏端（10寸/4寸）应用。屏端作为核心交互节点，已实现基于Android WebView加载H5应用，并完成了热词唤醒与VAD检测的技术准备。

### 1.2. 项目目标

核心目标是打通从语音输入到设备执行反馈的完整闭环，构建一个高效、智能、自然的语音控制系统。用户可通过部署在楼宇、办公室的屏端，用自然语言查询设备状态、控制设备运行，提升管理效率和用户体验。

### 1.3. 设计原则

- **模块化与高内聚**：将语音处理、大模型推理、业务逻辑等核心能力封装成独立的微服务，便于独立开发、扩展和维护。

- **用户体验优先**：交互流程设计需充分考虑响应延迟、结果反馈、异常处理等，确保交互的流畅与友好。

- **可扩展性**：架构设计应能适应未来更多设备类型、更复杂控制逻辑以及技术栈（如LLM模型）的升级。

- **安全性**：所有设备控制指令必须经过严格的鉴权和校验，防止误操作和恶意攻击。

## 2. 整体架构与链路设计

### 2.1. 系统架构图

系统整体由四部分组成：**屏端应用**、**智能语音服务**、**平台服务** 和 **物联网核心平台**。

```
+------------------+      +----------------------+      +--------------------+      +--------------------+
|     屏端应用      |      |     智能语音服务       |      |      平台服务        |      |   物联网核心平台      |
| (Android+H5)     |      | (Voice Service)      |      | (platform service) |      |   (IoT Platform)   |
+------------------+      +----------------------+      +--------------------+      +--------------------+
| - 热词唤醒/VAD    |<---> | - ASR (语音转文本)     | <--->| - 用户/设备信息查询   | <--->| - 设备指令下发       |
| - 录音&音频上传    |      | - LLM 意图识别        |      | - 设备控制接口封装    |       | - 设备状态同步       |
| - JSBridge通信    |      | - 对话管理 (上下文)    |      | - 鉴权与权限校验      |      | - 物模型管理        |
| - 结果展示/播报    |      | - TTS (文本转语音)     |      |                    |      |                    |
+------------------+      +----------------------+      +--------------------+      +--------------------+
```

### 2.2. 核心交互链路

用户一次完整的语音交互将经历以下链路：

1. **端侧**：用户说出唤醒词，屏端原生应用被激活，开始通过VAD检测录音。

2. **端侧 -> 服务端**：录音结束后，H5通过JSBridge获取音频数据，将其与屏端登录后获取的`SessionID`（或`Token`）一同上传至**智能语音服务**。

3. **智能语音服务**：
   a.  **ASR**：调用**私有化部署**的ASR服务，将接收到的音频文件转换为文本。
   b.  **信息准备**：根据`SessionID`，向**平台服务**查询当前屏端关联的空间信息、设备列表、设备实时状态，并从缓存（如Redis）中获取历史对话上下文。
   c.  **LLM推理**：将ASR识别的文本、设备信息、API接口信息（工具）、对话历史等整合进一个精心设计的Prompt中，调用大语言模型（LLM）进行推理。
   d.  **意图解析**：解析LLM返回的结果。结果通常是一个结构化的JSON，指明了用户的意图（如`control_device`）、目标设备、操作指令以及需要对用户进行澄清或确认的文本。

4. **智能语音服务 -> 平台服务**：
   a.  **直接执行**：如果意图明确且无需确认，则调用**平台服务**封装好的设备控制接口。
   b.  **请求确认**：如果LLM判断需要用户二次确认（如操作范围广、指令模糊），则先不执行，生成确认信息。

5. **平台服务 -> 物联网核心平台**：业务服务在收到控制请求后，进行权限校验，然后调用**物联网核心平台**的接口，下发指令给具体的物理设备。

6. **结果反馈**：
   a.  **物联网平台**将设备执行结果返回给**平台服务**。
   b.  **平台服务**将执行结果（成功/失败/设备最新状态）返回给**智能语音服务**。
   c.  **智能语音服务**根据结果，生成最终的反馈文案。
   d.  **TTS（可选）**：如果需要语音播报，调用**私有化部署**的TTS服务将反馈文案转换为语音。

7. **服务端 -> 端侧**：**智能语音服务**将最终结果（包含文本、语音URL、UI指令、设备状态更新数据等）通过一个结构化的JSON返回给屏端H5。

8. **端侧**：H5应用解析JSON，根据指令更新UI（如设备状态图标）、在对话界面显示结果卡片、或播放TTS语音。

### 2.3. 模块职责划分

- **屏端应用 (Android + H5)**：负责语音的捕获与上传、与用户的直接交互（UI展示、语音播报）、维护会话状态。

- **智能语音服务 (Voice Service)**：核心大脑。负责编排ASR、LLM、TTS等AI能力，管理对话流程和上下文，是语音交互逻辑的核心。

- **平台服务 (platform service)**：连接AI与IoT的桥梁。负责提供当前会话所需的业务数据（如设备列表），并对来自AI的控制指令进行鉴权和转译，调用底层平台接口。

- **物联网核心平台 (IoT Platform)**：现有基础设施。负责最终的设备通信和状态管理。

## 3. 系统交互流程（序列图）

```
sequenceDiagram
    participant User
    participant Screen_H5 as H5应用
    participant Screen_Native as Android原生
    participant VoiceService as 智能语音服务
    participant PlatformService as 平台服务
    participant IoTPlatform as 物联网平台

    User->>Screen_Native: 1. "小智管家" (唤醒词)
    Screen_Native->>User: (开始录音提示)
    User->>Screen_Native: 2. "把会议室的灯打开"
    Screen_Native-->>Screen_H5: 3. JSBridge: onVoiceRecordEnd(audioData)

    Screen_H5->>VoiceService: 4. POST /api/voice/interact (audio, sessionId)
    activate VoiceService

    VoiceService->>VoiceService: 5. ASR: 音频 -> "把会议室的灯打开"
    VoiceService->>PlatformService: 6. GET /api/context?sessionId=...
    activate PlatformService
    PlatformService-->>VoiceService: 7. 返回设备列表, 空间信息
    deactivate PlatformService

    VoiceService->>VoiceService: 8. 组装Prompt (含上下文、设备列表、Function Call定义)
    VoiceService->>VoiceService: 9. 调用LLM进行推理
    VoiceService-->>VoiceService: 10. LLM返回JSON: { function: "turnOnLight", args: { location: "会议室" } }

    alt 指令明确，无需确认
        VoiceService->>PlatformService: 11a. POST /api/device/control (action: "turnOn", deviceId: "light-01")
        activate PlatformService
        PlatformService->>IoTPlatform: 12a. 下发控制指令
        activate IoTPlatform
        IoTPlatform-->>PlatformService: 13a. 执行成功，返回新状态
        deactivate IoTPlatform
        PlatformService-->>VoiceService: 14a. 返回最终结果 { success: true, newState: "ON" }
        deactivate PlatformService
        VoiceService->>VoiceService: 15a. 生成反馈文案 "好的，会议室的灯已打开"
    else 指令模糊或重要，需要确认
        VoiceService->>VoiceService: 11b. LLM返回: { require_confirmation: true, message: "会议室有3盏灯，您想打开哪一盏？", options: ["全部", "灯A", "灯B"] }
        VoiceService->>VoiceService: 12b. 生成待确认的响应
    end

    VoiceService->>VoiceService: 16. (可选) TTS: "好的，会议室的灯已打开" -> audioUrl
    VoiceService-->>Screen_H5: 17. 返回JSON响应 { text: "...", audioUrl: "...", uiCard: {...}, deviceUpdates: [...] }
    deactivate VoiceService

    Screen_H5->>Screen_H5: 18. 解析响应，更新UI状态，显示卡片
    Screen_H5->>User: 19. 播放语音: "好的，会议室的灯已打开"
```

## 4. 关键技术点实现方案

### 4.1. 语音服务 (ASR/TTS)

鉴于系统需在私有化环境部署，不支持公网调用，我们选择基于开源模型的私有化部署方案。

- **技术选型推荐**:
  
  - **ASR (语音转文本)**:
    
    - **FunASR (阿里达摩院)**: 工业级语音识别开源模型，提供易于部署的服务化代码，支持热词定制，对中文场景优化较好，是理想的首选。
    
    - **Whisper (OpenAI)**: 具备强大的泛化能力和准确率，支持多种语言。部署相对简单，但针对特定领域（如设备名称）的优化可能需要额外的数据微调。
    
    - **WeNet (出门问问)**: 生产级的ASR开源工具包，提供完整的解决方案，但部署和定制的复杂度相对较高。
  
  - **TTS (文本转语音)**:
    
    - **VITS (Conditional Variational Autoencoder with Adversarial Learning)**: 目前效果最领先的开源TTS模型之一，音质自然度高。社区有许多预训练好的中文模型可以直接使用或进行微调。
    
    - **PaddleSpeech (百度飞桨)**: 包含多种TTS模型，提供完整的部署工具链，对中文支持友好。
    
    - **Coqui-TTS**: 一个活跃的开源TTS库，支持多种模型架构，社区资源丰富。

- **部署与运维考量**:
  
  - **硬件资源**: ASR和TTS模型，特别是要达到低延迟的实时效果，通常需要**配备NVIDIA GPU的服务器**进行推理。需要根据并发量需求评估所需的GPU型号（如T4, A10, A30）和数量。
  
  - **服务封装**: 将选定的ASR/TTS模型封装成独立的HTTP/gRPC服务，供智能语音服务调用。可以使用Triton Inference Server等工具简化模型部署和管理。
  
  - **模型优化与微调**: 为了提高对特定业务词汇（如“螺杆式冷水机组”、“新风系统”）的识别准确率，可能需要使用业务数据对ASR模型进行微调。同样，为了得到特定的发音人音色，也可能需要对TTS模型进行微调。

### 4.2. 智能对话核心 (LLM Agent)

#### 4.2.1. 对话上下文管理

- **目标**：让LLM理解多轮对话，实现“指代消解”（如第一句“打开会议室的灯”，第二句“把它调亮一点”，LLM应知道“它”指的是会议室的灯）。

- **实现方案**：
  
  1. **会话ID (Session ID)**：每次交互，前端必须携带标识当前屏端会话的唯一ID。
  
  2. **缓存存储**：使用Redis等内存数据库，以`Session ID`为Key，存储一个包含最近N轮对话（如5-10轮）的列表。每轮对话包含用户的输入（`user`角色）和模型的回答（`assistant`角色）。
  
  3. **上下文注入**：在每次调用LLM前，从Redis中读取历史对话，并按格式要求拼接到Prompt中。
  
  4. **过期策略**：为每个会话设置一个过期时间（如30分钟），超时后自动清除上下文，避免无限增长。

#### 4.2.2. Agent 与工具调用 (Function Calling)

这是实现LLM与物理世界联动的核心。针对您提出的问题，**强烈推荐使用 Function Calling (或Tool Calling)方案**，而非传统的MCP/ReAct框架。

- **方案对比**：
  
  - **Function Calling**：是当前主流大模型（如OpenAI GPT系列、Google Gemini、国产的通义千问、文心一言等）内建支持的能力。您只需在API请求中定义好可用的“工具”（即您的设备控制函数）的名称、描述和参数结构（JSON Schema）。LLM在理解用户意图后，会直接返回一个结构化的JSON，告诉你应该调用哪个函数以及传递什么参数。
    
    - **优点**：**高可靠性**、**低延迟**、**易于集成**。模型经过专门训练，能精确地生成符合预定义Schema的JSON，减少了后端复杂的文本解析和重试逻辑。
  
  - **MCP/ReAct**：这是一种基于Prompt的Agent框架，通过引导LLM进行“思考(Thought)->行动(Action)->观察(Observation)”的循环来完成任务。
    
    - **优点**：灵活性更高，理论上能处理更复杂的、需要多步推理和动态调整计划的任务。
    
    - **缺点**：对于设备控制这类意图相对明确的场景，显得过于复杂。它依赖LLM严格按照特定格式输出文本，容易出错，需要后端做大量的字符串解析和鲁棒性处理，增加了延迟和不确定性。

- **推荐方案与实现**：
  
  1. **定义工具集**：在智能语音服务中，以代码形式定义所有可供LLM调用的函数。例如：
     
     ```
     // Function Calling/Tool Calling 的工具定义示例
     [
      {
        "type": "function",
        "function": {
          "name": "controlDevice",
          "description": "控制指定的设备，可以执行打开、关闭、调节等操作",
          "parameters": {
            "type": "object",
            "properties": {
              "deviceId": {
                "type": "string",
                "description": "需要控制的设备唯一ID"
              },
              "action": {
                "type": "string",
                "enum": ["turnOn", "turnOff", "adjust"],
                "description": "要执行的操作类型"
              },
              "value": {
                "type": "any",
                "description": "调节操作的具体值，例如亮度百分比、空调温度"
              }
            },
            "required": ["deviceId", "action"]
          }
        }
      },
      {
        "type": "function",
        "function": {
          "name": "queryDeviceStatus",
          "description": "查询一个或多个设备的状态",
          "parameters": { ... }
        }
      }
     ]
     ```
  
  2. **调用LLM**：将上述工具定义与用户的提问、上下文、设备列表等一起发送给LLM。
  
  3. **解析与执行**：
     
     - 如果LLM返回了`tool_calls`字段，则解析出函数名和参数。
     
     - 根据函数名，在后端代码中找到对应的真实函数（如`PlatformService.controlDevice(...)`）并执行。
     
     - 将执行结果再次提交给LLM，让它根据执行结果生成对用户更自然的回复。

#### 4.2.3. Prompt 工程与管理

Prompt是LLM能力的“天花板”，需要精心设计。

- **System Prompt 结构**：
  
  ```
  # 角色
  你是一个智能楼宇管家，负责根据用户的语音指令控制和查询楼宇内的机电设备。
  
  # 规则
  - 始终使用中文与用户交流。
  - 你的回答必须简洁、友好、清晰。
  - 你的唯一能力是通过调用提供的工具来与设备交互。绝对不要虚构任何设备状态或操作结果。
  - 当用户指令模糊时，必须向用户提问以澄清意图，而不是猜测。例如，当有多个同名设备时，要询问用户具体是哪一个。
  - 对于关闭整个区域设备等重要操作，必须向用户二次确认。
  
  # 当前上下文信息
  - 当前时间: {{current_time}}
  - 屏端所在位置: {{location_name}}
  - 当前空间可用设备列表:
  {{device_list_json}} 
  // device_list_json 是一个包含设备ID、名称、类型、当前状态的JSON字符串
  ```

- **动态Prompt组装**：每次请求时，将上述System Prompt、历史对话（`messages`）、用户最新输入（`user`角色）以及工具定义（`tools`）整合在一起，形成完整的请求体。
  
  Prompt模板可以作为配置文件（如YAML）与服务一同部署，便于版本管理和迭代

### 4.3. 用户交互设计

#### 4.3.1. 操作确认机制

- **触发时机**：由LLM判断。在Prompt中明确规定，当操作涉及多个设备、关闭整个区域、或指令存在歧义时，应返回一个要求确认的响应。

- **实现方式**：LLM不返回`tool_calls`，而是返回一个包含`require_confirmation: true`标志和确认信息的文本。

- **前端呈现**：

- **10寸屏**：可以显示一个带有按钮的卡片，如：“您确定要关闭三楼所有的照明吗？[确定] [取消]”。用户可以直接点击。

- **4.寸屏**：主要通过语音确认：“您确定要关闭三楼所有的照明吗？请回答‘是’或‘否’”。用户的回答将作为下一轮对话的输入。
  
   **是**   **否**

#### 4.3.2. 结果反馈机制

- **核心原则**：反馈必须是**状态驱动**的，即不仅告诉用户“操作已执行”，还要告知“操作后的最新状态”。

- **API响应设计**：服务端返回给前端的JSON应包含明确的字段，详细设计见5.5.2章节"语音交互响应数据设计"。

- **响应结构概览**：
  
  ```json
  {
  "requestId": "550e8400-e29b-41d4-a716-446655440000",
  "success": true,
  "timestamp": 1703123456789,
  "cost": 1500,
  "data": {
    "responseText": "好的，会议室的灯已经为您打开",
    "audioUrl": "https://.../audio.mp3",
    "interactionType": "result",
    "ui": {
      "type": "ResultCard",
      "title": "操作成功",
      "content": "会议室主照明已开启"
    },
    "deviceUpdates": [
      {
        "deviceId": "light-01",
        "properties": {
          "power": "ON",
          "brightness": 100
        }
      }
    ]
  },
  "code": "0",
  "msg": "处理成功"
  }
  ```

- **多屏幕适配**：
  
  - **4寸屏**：主要通过语音播报结果，UI显示简化，只显示关键状态
  - **10寸屏**：完整渲染所有信息，提供丰富的图文和语音反馈

## 5. 核心服务详细设计：智能语音服务 (Voice Service)

本章节将详细阐述作为系统大脑的“智能语音服务”的具体实现方案。

### 5.1. 技术栈选型

- **开发语言**: **Python 3.10+**。作为AI和数据科学领域的首选语言，拥有最丰富的LLM、ASR、TTS相关生态库和框架。

- **Web框架**: **FastAPI**。基于Python类型提示的现代高性能Web框架，完美支持异步IO，非常适合处理调用多个外部服务（ASR, LLM, TTS, PlatformService）这种IO密集型场景，能有效降低请求延迟。

- **HTTP客户端**: **HTTPX**。支持同步和异步请求的现代HTTP客户端，与FastAPI搭配使用相得益彰。

### 5.2. 服务架构设计

采用分层架构，实现高内聚、低耦合，便于测试和维护。

```
+---------------------------------+
|      1. API接口层 (Controller)   |  <-- FastAPI
+---------------------------------+
|      2. 交互编排服务层（Service)   |  <-- 核心业务逻辑
+---------------------------------+
|    3. 外部服务集成层（Connector)   |  <-- 隔离外部依赖
|  - ASR Connector                |
|  - LLM Connector                |
|  - TTS Connector                |
|  - Platform Service Connector   |
+---------------------------------+
|       4. 数据/状态管理层          |  <-- Redis, Configs
+---------------------------------+
```

### 5.3. 核心模块详解

#### 5.3.1. API接口层

- **职责**: 接收来自屏端的HTTP请求，验证输入（如`SessionID`），解析请求体（如上传的音频文件）。

- **核心接口**:
  
  - `POST /api/voice/interact`: 接收音频文件和会话元数据，启动完整的语音交互流程，并异步返回最终结果。

#### 5.3.2. 交互编排服务层 (Orchestration Service)

- **职责**: 这是整个系统的核心业务逻辑所在，负责按顺序调用和编排所有下游服务。

- **工作流**:
  
  1. 接收API层传入的音频数据和`SessionID`。
  
  2. 调用`ASR Connector`进行语音转文本。
  
  3. 并行或串行地：
     a.  调用`Redis`获取历史对话上下文。
     b.  调用`PlatformServiceConnector`获取设备列表、空间信息。
  
  4. 调用`LLM Connector`的`generate`方法，传入所有信息（文本、上下文、设备、工具定义）获取LLM的响应（意图或回复）。
  
  5. **决策判断**:
     
     - 如果LLM返回的是工具调用（`tool_calls`），则调用`PlatformServiceConnector`执行设备控制。
     
     - 如果LLM返回的是需要用户确认的信息，则准备确认文案。
     
     - 如果LLM返回的是普通对话，则直接准备回复。
  
  6. 将执行结果或确认信息再次喂给`LLM Connector`，生成最终面向用户的自然语言回复。
  
  7. （可选）调用`TTS Connector`将回复文本转为语音。
  
  8. 组装最终的JSON响应体，返回给API层。

#### 5.3.3. 外部服务集成层 (Connectors)

- **职责**: 封装与所有外部服务（即使是私有化部署的）的通信细节，使得服务层不关心具体的实现技术。

- **`LLMConnector`**: 负责构建完整的请求体（prompt, tools, messages），调用私有化LLM的API，并解析返回的JSON。

- **`PlatformServiceConnector`**: 负责调用平台服务的RESTful API，处理如Token透传等认证逻辑。

### 5.4. 与关键系统交互细节

#### 5.4.1. 与大语言模型 (LLM) 的交互

交互是两阶段的：

1. **第一阶段（意图识别）**: 发送用户问题和工具定义，LLM返回需要调用的工具和参数。

2. **第二阶段（结果生成）**: 将工具的执行结果（如 "success", "failed: device offline"）回传给LLM，LLM根据这个结果生成一句通顺、人性化的回复（如“好的，已经打开了”或“抱歉，设备现在离线，无法操作”）。

#### 5.4.2. 与平台服务和物联平台的交互

- **认证**: `Voice Service`在调用`platform service`时，需要透传从屏端获取的`SessionID`或`Token`，由`platform service`完成最终的权限校验，确保该用户有权操作其声明的设备。

- **数据一致性**: `Voice Service`不应缓存设备状态。每次交互都应通过`platform service`实时查询，以获取最新的设备状态，确保LLM的决策基于准确的信息。

### 5.5. 核心接口定义 (初步)

为确保各服务模块间的高效协作，我们初步定义以下核心RESTful API接口。所有接口都应遵循统一的API版本化策略（如`/api/v1/...`）和错误处理机制。

#### 5.5.1. 屏端应用 -> 智能语音服务

这是语音交互流程的起点，接口设计与安卓端应用开发说明文档中定义的服务端接口保持一致。

**1. 语音交互接口**

- **Endpoint**: 由H5配置的`serverUrl`决定（如 `/api/voice/interact`）

- **Content-Type**: `application/json`

- **描述**: 客户端（屏端H5）在录音结束后，调用此接口上传音频数据和会话元数据，启动完整的语音交互处理流程。

- **请求体格式**:
  
  ```json
  {
    "requestId": "550e8400-e29b-41d4-a716-446655440000",
    "token": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0Y",
    "timestamp": 1703123456789,
    "audioData": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT...",
    "audioFormat": {
      "sampleRate": 16000,
      "channels": 1,
      "encoding": "PCM_16BIT"
    },
    "extend": {
      "authorization": "bearer 6dd7cc7a-f44e-45cc-ba5b-c95ef2e95dc5",
      "buildingId": "1942499471542292482"
    }
  }
  ```

- **成功响应 (200 OK)**:
  
  - **Body**: `application/json`。响应结构详见下一章节的详细设计。
    
    ```json
    {
      "requestId": "550e8400-e29b-41d4-a716-446655440000",
      "success": true,
      "timestamp": 1703123456789,
      "cost": 1500,
      "data": {
        // 详细的交互响应数据，见下一章节设计
      },
      "code": "0",
      "msg": "处理成功"
    }
    ```

#### 5.5.2. 语音交互响应数据设计

本章节详细设计语音交互接口响应中的`data`字段内容，该字段包含了与用户界面交互相关的所有信息，需要支持10寸屏和4寸屏的不同交互方式。

##### 5.5.2.1. 响应数据结构概览

语音交互响应的`data`字段采用灵活的结构设计，支持多种交互场景：

```json
{
  "data": {
    "text": "识别的用户语音文本",
    "responseText": "系统回复文本",
    "audioUrl": "TTS音频文件URL（可选）",
    "audioBase64": "Base64编码的TTS音频数据（可选）",
    "interactionType": "交互类型标识",
    "ui": {
      "type": "UI组件类型",
      "config": "UI配置参数"
    },
    "deviceUpdates": [
      "设备状态更新数据"
    ],
    "contextData": {
      "会话上下文相关数据"
    }
  }
}
```

##### 5.5.2.2. 核心字段定义

**基础文本字段**

| 字段名          | 类型     | 必填  | 说明                            |
| ------------ | ------ | --- | ----------------------------- |
| text         | String | 否   | ASR识别的用户语音文本内容                |
| responseText | String | 是   | 系统生成的回复文本，用于显示和TTS            |
| audioUrl     | String | 否   | TTS生成的音频文件URL地址               |
| audioBase64  | String | 否   | Base64编码的TTS音频数据，与audioUrl二选一 |

**交互类型标识**

`interactionType`字段用于标识当前交互的类型，H5根据此字段决定UI展示方式：

| 类型值             | 说明     | 适用场景            |
| --------------- | ------ | --------------- |
| `result`        | 操作结果反馈 | 设备控制成功/失败后的结果展示 |
| `confirmation`  | 需要用户确认 | 重要操作前的二次确认      |
| `clarification` | 需要用户澄清 | 指令模糊时的澄清询问      |
| `query_result`  | 查询结果展示 | 设备状态查询、信息查询等    |
| `error`         | 错误信息   | 操作失败、系统异常等错误情况  |
| `conversation`  | 普通对话   | 非设备操作的日常对话      |

##### 5.5.2.3. UI组件设计

**UI字段结构**

```json
{
  "ui": {
    "type": "UI组件类型",
    "title": "标题文本",
    "content": "主要内容",
    "actions": [
      {
        "type": "按钮类型",
        "text": "按钮文本",
        "value": "按钮值",
        "style": "按钮样式"
      }
    ],
    "data": {
      "组件特定的数据"
    }
  }
}
```

**支持的UI组件类型**

**1. ResultCard - 操作结果卡片**

```json
{
  "ui": {
    "type": "ResultCard",
    "title": "操作成功",
    "content": "会议室的灯已经为您打开",
    "status": "success",
    "data": {
      "deviceName": "会议室主照明",
      "operation": "开启",
      "newState": {
        "power": "ON",
        "brightness": 100
      }
    }
  }
}
```

**2. ConfirmationCard - 确认卡片**

```json
{
  "ui": {
    "type": "ConfirmationCard",
    "title": "操作确认",
    "content": "您确定要关闭三楼所有的照明设备吗？",
    "actions": [
      {
        "type": "confirm",
        "text": "确定",
        "value": "yes",
        "style": "primary"
      },
      {
        "type": "cancel",
        "text": "取消",
        "value": "no",
        "style": "secondary"
      }
    ]
  }
}
```

**3. OptionCard - 选项卡片**

```json
{
  "ui": {
    "type": "OptionCard",
    "title": "请选择设备",
    "content": "会议室有多个照明设备，请选择要控制的设备：",
    "actions": [
      {
        "type": "option",
        "text": "主照明灯",
        "value": "light-01"
      },
      {
        "type": "option",
        "text": "辅助照明灯",
        "value": "light-02"
      },
      {
        "type": "option",
        "text": "全部照明",
        "value": "all-lights"
      }
    ]
  }
}
```

**4. QueryResultCard - 查询结果卡片**

```json
{
  "ui": {
    "type": "QueryResultCard",
    "title": "设备状态查询",
    "content": "当前会议室设备状态如下：",
    "data": {
      "devices": [
        {
          "name": "主照明灯",
          "status": "开启",
          "details": "亮度: 80%"
        },
        {
          "name": "中央空调",
          "status": "运行中",
          "details": "温度: 24°C, 模式: 制冷"
        }
      ]
    }
  }
}
```

##### 5.5.2.4. 设备状态更新数据

`deviceUpdates`字段用于通知H5更新界面上的设备状态显示：

```json
{
  "deviceUpdates": [
    {
      "deviceId": "light-01",
      "deviceName": "会议室主照明",
      "properties": {
        "power": "ON",
        "brightness": 100,
        "color": "#FFFFFF"
      },
      "timestamp": 1703123456789
    },
    {
      "deviceId": "ac-01",
      "deviceName": "中央空调",
      "properties": {
        "power": "ON",
        "mode": "cool",
        "temperature": 24,
        "fanSpeed": "auto"
      },
      "timestamp": 1703123456789
    }
  ]
}
```

##### 5.5.2.5. 上下文数据

`contextData`字段用于传递会话上下文相关的数据：

```json
{
  "contextData": {
    "sessionId": "session-screen01-user123",
    "conversationId": "conv-20231027-001",
    "lastOperation": {
      "type": "device_control",
      "deviceId": "light-01",
      "action": "turnOn",
      "timestamp": 1703123456789
    },
    "pendingConfirmation": {
      "operationId": "op-20231027-001",
      "expiresAt": 1703123486789
    }
  }
}
```

##### 5.5.2.6. 多屏幕尺寸适配设计

**10寸屏适配**

10寸屏具有较大的显示空间，支持丰富的UI交互：

**特点**：
- 支持完整的UI卡片显示
- 可以显示详细的文本内容和操作按钮
- 支持复杂的交互流程

**适配策略**：
```json
{
  "data": {
    "responseText": "好的，会议室的灯已经为您打开",
    "audioUrl": "https://cdn.example.com/audio/response.mp3",
    "interactionType": "result",
    "ui": {
      "type": "ResultCard",
      "title": "操作成功",
      "content": "会议室主照明已开启，当前亮度100%",
      "status": "success",
      "data": {
        "deviceName": "会议室主照明",
        "operation": "开启",
        "details": [
          "设备状态：开启",
          "亮度：100%",
          "功耗：45W"
        ]
      }
    },
    "deviceUpdates": [
      {
        "deviceId": "light-01",
        "properties": {
          "power": "ON",
          "brightness": 100
        }
      }
    ]
  }
}
```

**4寸屏适配**

4寸屏显示空间有限，主要依靠语音反馈：

**特点**：
- 主要通过语音播报结果
- UI显示简化，只显示关键状态
- 设备状态更新仍然需要支持

**适配策略**：
```json
{
  "data": {
    "responseText": "好的，会议室的灯已经为您打开",
    "audioBase64": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT...",
    "interactionType": "result",
    "ui": {
      "type": "SimpleStatus",
      "content": "操作成功",
      "status": "success"
    },
    "deviceUpdates": [
      {
        "deviceId": "light-01",
        "properties": {
          "power": "ON",
          "brightness": 100
        }
      }
    ]
  }
}
```

**屏幕尺寸检测与自适应**

H5应用可以通过以下方式检测屏幕尺寸并自适应处理：

```javascript
// 检测屏幕尺寸
function getScreenType() {
    const width = window.screen.width;
    const height = window.screen.height;

    // 根据分辨率判断屏幕类型
    if (width >= 800 || height >= 800) {
        return '10-inch';
    } else {
        return '4-inch';
    }
}

// 根据屏幕类型处理响应数据
function handleServerResponse(data) {
    const response = JSON.parse(data);
    const screenType = getScreenType();

    if (response.success) {
        // 播放语音（所有屏幕都支持）
        if (response.data.audioUrl || response.data.audioBase64) {
            playResponseAudio(response.data);
        }

        // 根据屏幕类型显示UI
        if (screenType === '10-inch') {
            // 10寸屏：显示完整UI卡片
            displayFullUI(response.data.ui);
        } else {
            // 4寸屏：只显示简化状态
            displaySimpleStatus(response.data.ui);
        }

        // 更新设备状态（所有屏幕都需要）
        updateDeviceStatus(response.data.deviceUpdates);
    }
}
```

##### 5.5.2.7. 完整响应示例

**设备控制成功响应**

```json
{
  "requestId": "550e8400-e29b-41d4-a716-446655440000",
  "success": true,
  "timestamp": 1703123456789,
  "cost": 1500,
  "data": {
    "text": "把会议室的灯打开",
    "responseText": "好的，会议室的灯已经为您打开",
    "audioUrl": "https://cdn.example.com/audio/response_001.mp3",
    "interactionType": "result",
    "ui": {
      "type": "ResultCard",
      "title": "操作成功",
      "content": "会议室主照明已开启",
      "status": "success",
      "data": {
        "deviceName": "会议室主照明",
        "operation": "开启",
        "newState": {
          "power": "ON",
          "brightness": 100
        }
      }
    },
    "deviceUpdates": [
      {
        "deviceId": "light-01",
        "deviceName": "会议室主照明",
        "properties": {
          "power": "ON",
          "brightness": 100,
          "color": "#FFFFFF"
        },
        "timestamp": 1703123456789
      }
    ],
    "contextData": {
      "sessionId": "session-screen01-user123",
      "conversationId": "conv-20231027-001",
      "lastOperation": {
        "type": "device_control",
        "deviceId": "light-01",
        "action": "turnOn",
        "timestamp": 1703123456789
      }
    }
  },
  "code": "0",
  "msg": "处理成功"
}
```

**需要确认的操作响应**

```json
{
  "requestId": "550e8400-e29b-41d4-a716-446655440001",
  "success": true,
  "timestamp": 1703123456789,
  "cost": 1200,
  "data": {
    "text": "关闭三楼所有的灯",
    "responseText": "您确定要关闭三楼所有的照明设备吗？这将影响12个设备。",
    "audioUrl": "https://cdn.example.com/audio/confirm_001.mp3",
    "interactionType": "confirmation",
    "ui": {
      "type": "ConfirmationCard",
      "title": "操作确认",
      "content": "您确定要关闭三楼所有的照明设备吗？这将影响12个设备。",
      "actions": [
        {
          "type": "confirm",
          "text": "确定关闭",
          "value": "confirm_close_all_lights_floor3",
          "style": "primary"
        },
        {
          "type": "cancel",
          "text": "取消操作",
          "value": "cancel",
          "style": "secondary"
        }
      ]
    },
    "contextData": {
      "sessionId": "session-screen01-user123",
      "conversationId": "conv-20231027-001",
      "pendingConfirmation": {
        "operationId": "op-close-all-lights-floor3",
        "operation": "close_all_lights",
        "scope": "floor3",
        "deviceCount": 12,
        "expiresAt": 1703123486789
      }
    }
  },
  "code": "0",
  "msg": "处理成功"
}
```

#### 5.5.3. 智能语音服务 -> 平台服务

这是 `Voice Service` 获取业务信息和执行控制的接口。

**1. 获取AI上下文信息**

- **Endpoint**: `GET /api/v1/ai/context`

- **描述**: `Voice Service` 在进行LLM推理前，调用此接口获取必要的上下文信息，用于构建Prompt。

- **认证**: `Voice Service` 需将从屏端获取的扩展参数透传给平台服务。
  
  - **Header**: `Authorization: <authorization_from_extend>`
  - **Query Parameters**:
    - `buildingId`: 楼宇空间ID（从extend参数获取）
    - 其他扩展参数根据业务需要添加

- **请求示例**:
  
  ```
  GET /api/v1/ai/context?buildingId=1942499471542292482
  Authorization: bearer 6dd7cc7a-f44e-45cc-ba5b-c95ef2e95dc5
  ```

- **成功响应 (200 OK)**:
  
  - **Body**: `application/json`
    
    ```json
    {
      "user": {
        "id": "user123",
        "name": "张三"
      },
      "location": {
        "id": "space-01-floor3-meeting-room-A",
        "name": "会议室A",
        "fullName": "A座/三楼/会议室A"
      },
      "devices": [
        {
          "deviceId": "light-01",
          "name": "会议室主照明",
          "type": "light",
          "status": {
            "power": "OFF",
            "brightness": 0
          },
          "aliases": ["灯", "照明灯"]
        },
        {
          "deviceId": "ac-01",
          "name": "中央空调",
          "type": "air-conditioner",
          "status": {
            "power": "ON",
            "mode": "cool",
            "temperature": 24
          },
          "aliases": ["空调"]
        }
      ]
    }
    ```

**2. 执行设备控制**

- **Endpoint**: `POST /api/v1/ai/control`

- **描述**: `Voice Service` 在解析出LLM的设备控制意图后，调用此接口请求平台服务执行具体操作。

- **认证**: 同上，需要透传认证信息和扩展参数。
  
  - **Header**: `Authorization: <authorization_from_extend>`

- **请求体**: `application/json`
  
  ```json
  {
    "toolCallId": "call_abc123",
    "functionName": "controlDevice",
    "arguments": {
      "deviceId": "light-01",
      "action": "turnOn",
      "value": null
    },
    "extend": {
      "buildingId": "1942499471542292482"
    }
  }
  ```
  
  *注：这里的`arguments`结构直接来自于LLM返回的Function Calling参数，`extend`字段透传屏端的扩展参数。*

- **成功响应 (200 OK)**:
  
  - **Body**: `application/json`。返回操作的执行结果，`Voice Service` 会将此结果再次提供给LLM以生成最终的自然语言回复。
    
    ```json
    {
      "toolCallId": "call_abc123",
      "status": "success",
      "message": "设备已成功开启",
      "newState": {
        "power": "ON",
        "brightness": 100
      }
    }
    ```

### 5.6. 部署方案建议

- **容器化**: 使用**Docker**将`Voice Service`以及其依赖的Python环境打包成一个独立的镜像。

- **服务部署**:
  
  - `Voice Service`: 可以部署在CPU服务器上，因为它主要是IO密集型任务。
  
  - `ASR/TTS/LLM Service`: **必须部署在配备高性能GPU的服务器上**。

- **配置管理**: 使用环境变量或配置文件来管理数据库地址、各服务API端点等，实现一次构建，多环境部署。

## 6. 总结

本方案提出了一套基于主流LLM的Function Calling能力的物联网语音交互解决方案。它通过将AI能力与平台服务解耦，构建了一条从语音输入到设备执行的清晰、可扩展的技术链路。方案重点关注了对话管理、工具调用、Prompt工程和多端适配的用户交互设计，旨在为您提供一个既具备先进性又切实可行的实施蓝图。建议在开发初期，优先打通核心链路，然后逐步优化Prompt和交互细节，以达到最佳的用户体验。
