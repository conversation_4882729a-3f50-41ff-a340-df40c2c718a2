# InxVision Voice Service

智能语音交互服务，为物联网设备控制提供语音交互能力。

## 🎯 项目概述

InxVision Voice Service 是一个基于 FastAPI 的智能语音服务，实现了完整的语音交互链路：

- **语音识别 (ASR)**: 将用户语音转换为文本
- **意图理解 (LLM)**: 使用大语言模型理解用户意图并生成设备控制指令
- **设备控制**: 通过 Function Calling 执行具体的设备操作
- **语音合成 (TTS)**: 将系统回复转换为语音
- **多屏适配**: 支持 10寸屏和 4寸屏的不同交互方式

## 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   屏端应用      │───▶│  Voice Service  │───▶│  Platform API   │
│   (H5/Android)  │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │   AI Services   │
                    │  ASR/LLM/TTS    │
                    └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.10+
- Redis 6.0+
- 支持 Function Calling 的 LLM 服务

### 安装依赖

```bash
# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 配置环境

```bash
# 复制环境配置文件
cp .env.example .env

# 编辑配置文件，填入真实的 LLM API 配置
vim .env
```

### 启动服务

```bash
# 开发模式启动
python -m src.main

# 或使用 uvicorn
uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

### 访问文档

- API 文档: http://localhost:8000/docs
- 健康检查: http://localhost:8000/health

## 📋 开发状态

当前项目处于 PoC 验证阶段，按照 `tasklist.md` 进行开发：

- ✅ **阶段一**: 项目基础搭建
  - ✅ 项目结构创建
  - ✅ FastAPI 框架集成
  - ✅ 配置管理和日志系统
  - ✅ 核心接口骨架

- 🔄 **阶段二**: AI服务集成层 (进行中)
- ⏳ **阶段三**: 核心业务逻辑
- ⏳ **阶段四**: Function Calling实现
- ⏳ **阶段五**: 测试和验证
- ⏳ **阶段六**: 部署准备

## 🔧 技术栈

- **Web框架**: FastAPI
- **HTTP客户端**: HTTPX
- **数据验证**: Pydantic
- **缓存**: Redis
- **日志**: Structlog
- **测试**: Pytest
- **代码格式**: Black + isort + flake8

## 📖 API 接口

### 语音交互接口

```http
POST /api/v1/voice/interact
Content-Type: application/json

{
  "requestId": "550e8400-e29b-41d4-a716-446655440000",
  "token": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0Y",
  "timestamp": 1703123456789,
  "audioData": "base64_encoded_audio_data",
  "audioFormat": {
    "sampleRate": 16000,
    "channels": 1,
    "encoding": "PCM_16BIT"
  },
  "extend": {
    "authorization": "bearer token",
    "buildingId": "1942499471542292482"
  }
}
```

详细的 API 文档请参考 `语音交互整体技术方案.md`。

## 🧪 测试

```bash
# 运行所有测试
pytest

# 运行测试并生成覆盖率报告
pytest --cov=src --cov-report=html

# 运行特定测试
pytest tests/test_api.py -v
```

## 📁 项目结构

```
inxvision-voice-service/
├── src/                    # 源代码目录
│   ├── __init__.py
│   ├── main.py            # 主应用文件
│   ├── config.py          # 配置管理
│   ├── logger.py          # 日志配置
│   ├── models.py          # 数据模型
│   ├── connectors/        # 外部服务连接器
│   ├── services/          # 业务逻辑服务
│   └── utils/             # 工具函数
├── tests/                 # 测试文件
├── docs/                  # 文档
├── requirements.txt       # 依赖列表
├── .env.example          # 环境配置示例
├── tasklist.md           # 开发任务列表
└── README.md             # 项目说明
```

## 🤝 开发指南

1. 遵循 `tasklist.md` 中的开发计划
2. 使用 Black 格式化代码: `black src/`
3. 使用 isort 整理导入: `isort src/`
4. 运行代码检查: `flake8 src/`
5. 编写单元测试并确保通过
6. 提交前运行完整测试套件

## 📄 许可证

[MIT License](LICENSE)
