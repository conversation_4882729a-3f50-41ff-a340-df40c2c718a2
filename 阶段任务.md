接下来的首要任务，就是将方案付诸行动：启动一个最小可行性验证（PoC）的并行开发。有了明确的API定义（我们称之为“契约”），前后端团队现在可以并行工作，而不需要互相等待，核心原则： 以《语音交互整体技术方案.md》中的API定义为“契约”和“唯一真相”。三个团队并行开发，通过这份契约来确保最后能顺利集成。

我建议我们将PoC开发分为三个并行的工作流：

## 智能语音服务 (Voice Service) 团队 -【核心任务】

这是整个PoC的核心，目标是验证AI能力的编排逻辑。

- 任务一：搭建服务骨架
  
  - 使用Python和FastAPI，快速创建一个Web服务项目。
  
  - 实现 POST /api/v1/voice/interact 这个HTTP接口的骨架。

- 任务二：实现“模拟”的AI链路
  
  - 模拟ASR：在接口内部，暂时忽略上传的音频，直接返回一个固定的文本，例如 {"text": "把会议室的灯打开"}。
  
  - 模拟平台服务：编写一个PlatformServiceConnector的模拟实现 (mock)。当调用 GET /api/v1/ai/context 时，它不发出真实HTTP请求，而是直接返回我们在文档中定义的那个包含设备列表的JSON对象。
  
  - 集成真实LLM：这是最关键的一步。将模拟的ASR文本、模拟的平台上下文信息，以及我们在方案中定义的tools（Function Call），组装成一个完整的Prompt，真实地调用你选定的大语言模型API。
  
  - 解析LLM并模拟执行：
    
    - 接收LLM返回的tool_calls。
    
    - 调用PlatformServiceConnector的模拟controlDevice方法。这个方法也只是打印一条日志，并返回一个成功的JSON。
    
    - 将执行结果再次提交给LLM，获取最终的自然语言回复。
  
  - 模拟TTS：暂时无需集成TTS服务，直接将LLM生成的最终回复文本放入返回给前端的JSON中即可。

- 目标：在不依赖任何其他真实服务的情况下，独立验证“接收请求 -> 组装信息 -> 调用LLM -> 解析并执行工具 -> 生成回复”这一核心逻辑能否跑通，并观察LLM的行为是否符合预期。

## 平台服务 (platform service) 团队 -【契约实现】

该团队的目标是根据API契约，实现真实的业务接口。

- 任务一：实现上下文接口
  
  - 开发 GET /api/v1/ai/context 接口。
  
  - 确保接口能根据传入的 Authorization 头中的Token，正确地查询并返回指定用户/屏端下的空间信息和设备列表。
  
  - 返回的JSON结构必须与我们在文档中定义的完全一致。

- 任务二：实现控制接口
  
  - 开发 POST /api/v1/ai/control 接口。
  
  - 接口接收来自Voice Service的控制指令，进行权限校验，并调用物联网核心平台完成真实的设备操作。
  
  - 返回的JSON结构必须与文档定义一致。

- 测试方法：团队可以使用Postman或Apifox等API工具，模拟Voice Service发送请求，独立测试和验证自己的接口功能，无需等待Voice Service开发完成。

## 屏端应用 (H5) 团队 -【前端开发】

该团队的目标是开发用户交互界面，并准备好与后端对接。

- 任务一：开发对话UI
  
  - 根据设计，开始开发对话气泡、结果卡片等UI组件。

- 任务二：模拟API调用与响应
  
  - 在H5代码中，创建一个“模拟发送”按钮。
  
  - 点击按钮后，模拟调用 POST /api/v1/voice/interact 接口。
  
  - 在代码中创建一个写死的JSON对象，其结构与我们在文档中定义的Voice Service响应体完全一致。
  
  - 开发解析这个模拟JSON的逻辑，并用其中的数据（如displayText, ttsUrl, ui, deviceUpdates）来正确地更新UI界面。

- 目标：在后端接口尚未完成时，提前完成所有前端UI和数据处理逻辑的开发。一旦后端接口就绪，只需将模拟数据替换为真实的API调用即可。
