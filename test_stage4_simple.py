"""
阶段四简化测试 - Function Calling实现验证
"""
import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_tool_manager():
    """测试工具管理器基础功能"""
    print("🔧 Testing Tool Manager...")
    
    try:
        from src.tools import ToolManager
        print("✅ ToolManager import successful")
        
        # 创建工具管理器
        manager = ToolManager()
        
        # 测试工具注册
        tools = manager.get_available_tools()
        print(f"✅ Available tools: {tools}")
        
        # 测试工具定义
        definitions = manager.get_tool_definitions()
        print(f"✅ Tool definitions count: {len(definitions)}")
        
        # 测试单个工具调用
        result = await manager.execute_tool_call(
            tool_name="controlDevice",
            call_id="test-001",
            parameters={
                "deviceId": "light-01",
                "deviceName": "测试灯光",
                "action": "turnOn"
            },
            context={
                "authorization": "Bearer test-token",
                "buildingId": "test-building",
                "permissions": {"canControl": True, "canQuery": True}
            }
        )
        
        print(f"✅ Tool execution result:")
        print(f"   Success: {result.success}")
        print(f"   Message: {result.message}")
        print(f"   Status: {result.status.value}")
        print(f"   Execution time: {result.execution_time_ms}ms")
        
        await manager.close()
        print("✅ Tool manager closed")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool Manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_voice_service_integration():
    """测试语音服务集成"""
    print("\n🎤 Testing Voice Service Integration...")
    
    try:
        from src.services import VoiceInteractionService
        from src.models import VoiceInteractRequest, AudioFormat, ExtendParams
        
        print("✅ VoiceInteractionService import successful")
        
        # 创建服务实例
        service = VoiceInteractionService()
        
        # 检查工具管理器是否正确集成
        available_tools = service.tool_manager.get_available_tools()
        print(f"✅ Service has tools: {available_tools}")
        
        # 创建测试请求
        request = VoiceInteractRequest(
            requestId="stage4-test-001",
            token="stage4-test-token",
            timestamp=1703123456789,
            audioData="A" * 200,  # 长度200，触发设备控制场景
            audioFormat=AudioFormat(
                sampleRate=16000,
                channels=1,
                encoding="PCM_16BIT"
            ),
            extend=ExtendParams(
                authorization="Bearer stage4-test-token",
                buildingId="1942499471542292482"
            )
        )
        
        # 处理请求
        response = await service.process_voice_interaction(request)
        
        print(f"✅ Voice interaction result:")
        print(f"   Success: {response.success}")
        print(f"   User text: {response.data.text}")
        print(f"   Response: {response.data.responseText}")
        print(f"   Type: {response.data.interactionType}")
        print(f"   Processing time: {response.cost}ms")
        
        await service.close()
        print("✅ Voice service closed")
        
        return True
        
    except Exception as e:
        print(f"❌ Voice Service test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_llm_tool_integration():
    """测试LLM工具集成"""
    print("\n🧠 Testing LLM Tool Integration...")
    
    try:
        from src.connectors import LLMConnector
        from src.tools import ToolManager
        
        # 创建工具管理器
        tool_manager = ToolManager()
        
        # 创建LLM连接器（注入工具管理器）
        llm_connector = LLMConnector(tool_manager)
        
        # 检查工具定义
        tools = llm_connector.tools
        print(f"✅ LLM has {len(tools)} tools")
        
        for tool in tools:
            func_info = tool["function"]
            print(f"   - {func_info['name']}: {func_info['description']}")
        
        await tool_manager.close()
        await llm_connector.close()
        print("✅ LLM tool integration test completed")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM Tool integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """运行所有测试"""
    print("🧪 Stage 4: Function Calling Implementation - Simple Tests")
    print("=" * 60)
    
    tests = [
        ("Tool Manager", test_tool_manager),
        ("Voice Service Integration", test_voice_service_integration),
        ("LLM Tool Integration", test_llm_tool_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} Test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 Test Summary")
    print("=" * 60)
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall Result: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        print("🎉 All Stage 4 tests PASSED! Function Calling implementation is complete!")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")

if __name__ == "__main__":
    asyncio.run(main())
