"""
阿里云TTS集成测试
测试阿里云TTS远程服务和TTS连接器集成
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.connectors.tts_ali_remote_service import AliTTSRemoteService
from src.connectors.tts_connector import TTSConnector
from src.config import settings


async def test_ali_tts_service():
    """测试阿里云TTS远程服务"""
    print("🧪 Test 1: Ali TTS Remote Service")
    print("-" * 40)
    
    try:
        # 创建阿里云TTS服务实例
        ali_tts = AliTTSRemoteService()
        
        print(f"📋 Service configured: {ali_tts.is_configured()}")
        print(f"📋 Config info: {ali_tts.get_config_info()}")
        
        if not ali_tts.is_configured():
            print("⚠️  Ali TTS not configured, skipping synthesis test")
            return True
        
        # 测试文本合成
        test_text = "你好，这是阿里云TTS语音合成测试。"
        
        print(f"🎤 Testing text synthesis: {test_text}")
        
        # 测试POST请求方式
        result = await ali_tts.text_to_speech(
            text=test_text,
            params={
                'format': 'wav',
                'sample_rate': 16000,
                'voice': 'xiaoyun',
                'volume': 50
            },
            use_post=True
        )
        
        print(f"📊 POST Result: {result}")
        
        if result['success']:
            print(f"✅ POST synthesis successful!")
            print(f"   Audio file: {result['audio_file']}")
            print(f"   Audio URL: {result['audio_url']}")
            print(f"   File size: {result.get('file_size', 0)} bytes")
        else:
            print(f"❌ POST synthesis failed: {result['error']}")
        
        # 测试GET请求方式
        print("\n🔄 Testing GET request method...")
        result_get = await ali_tts.text_to_speech(
            text=test_text,
            params={
                'format': 'mp3',
                'sample_rate': 16000,
                'voice': 'xiaoyun'
            },
            use_post=False
        )
        
        print(f"📊 GET Result: {result_get}")
        
        if result_get['success']:
            print(f"✅ GET synthesis successful!")
            print(f"   Audio file: {result_get['audio_file']}")
            print(f"   Audio URL: {result_get['audio_url']}")
        else:
            print(f"❌ GET synthesis failed: {result_get['error']}")
        
        return result['success'] or result_get['success']
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_tts_connector_integration():
    """测试TTS连接器集成"""
    print("\n🧪 Test 2: TTS Connector Integration")
    print("-" * 40)
    
    try:
        # 创建TTS连接器
        tts_connector = TTSConnector()
        
        print(f"📋 Service name: {tts_connector.get_service_name()}")
        print(f"📋 Ali TTS configured: {tts_connector.ali_tts_service.is_configured()}")
        print(f"📋 Ali TTS enabled: {settings.ali_tts_enabled}")
        
        # 测试文本转语音
        test_text = "欢迎使用智能语音交互系统，我可以帮助您控制各种设备。"
        
        print(f"🎤 Testing TTS connector with text: {test_text}")
        
        # 测试返回URL方式
        result = await tts_connector.text_to_speech(
            text=test_text,
            voice_config={'voice': 'xiaoyun', 'volume': 60},
            output_format='wav',
            return_base64=False,
            request_id='test-tts-001'
        )
        
        print(f"📊 TTS Result: {result}")
        
        if isinstance(result, str) and result.startswith('http'):
            print(f"✅ TTS synthesis successful!")
            print(f"   Audio URL: {result}")
        else:
            print(f"❌ TTS synthesis failed or returned unexpected result")
        
        # 测试返回Base64方式
        print("\n🔄 Testing Base64 return format...")
        result_base64 = await tts_connector.text_to_speech(
            text="这是Base64格式测试",
            voice_config={'voice': 'xiaoyun'},
            output_format='mp3',
            return_base64=True,
            request_id='test-tts-002'
        )
        
        if isinstance(result_base64, str) and len(result_base64) > 100:
            print(f"✅ Base64 TTS synthesis successful!")
            print(f"   Base64 length: {len(result_base64)} characters")
        else:
            print(f"❌ Base64 TTS synthesis failed")
        
        await tts_connector.close()
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_tts_error_handling():
    """测试TTS错误处理"""
    print("\n🧪 Test 3: TTS Error Handling")
    print("-" * 40)
    
    # 测试无效凭证的情况
    original_enabled = settings.ali_tts_enabled
    original_app_key = getattr(settings, 'ali_tts_app_key', '')
    original_token = getattr(settings, 'ali_tts_token', '')
    
    try:
        # 启用阿里云TTS但使用无效凭证
        settings.ali_tts_enabled = True
        settings.ali_tts_app_key = "invalid_app_key"
        settings.ali_tts_token = "invalid_token"
        
        tts_connector = TTSConnector()
        
        print(f"📋 Service name: {tts_connector.get_service_name()}")
        print(f"📋 Testing with invalid credentials...")
        
        # 这应该会失败
        try:
            result = await tts_connector.text_to_speech(
                text="测试错误处理",
                voice_config={'voice': 'xiaoyun'},
                output_format='wav',
                return_base64=False,
                request_id='test-error-001'
            )
            
            print(f"❌ Expected failure but got result: {result}")
            return False
            
        except Exception as e:
            print(f"✅ Expected exception caught: {type(e).__name__}: {e}")
            return True
        
    finally:
        # 恢复原始配置
        settings.ali_tts_enabled = original_enabled
        settings.ali_tts_app_key = original_app_key
        settings.ali_tts_token = original_token


async def test_configuration_scenarios():
    """测试不同配置场景"""
    print("\n🧪 Test 4: Configuration Scenarios")
    print("-" * 40)
    
    scenarios = [
        ("Ali TTS Disabled", False, "", ""),
        ("Ali TTS Enabled but Not Configured", True, "", ""),
        ("Ali TTS Enabled and Configured", True, "test_key", "test_token")
    ]
    
    results = []
    
    for scenario_name, enabled, app_key, token in scenarios:
        print(f"\n📋 Testing scenario: {scenario_name}")
        
        # 临时设置配置
        original_enabled = settings.ali_tts_enabled
        original_app_key = getattr(settings, 'ali_tts_app_key', '')
        original_token = getattr(settings, 'ali_tts_token', '')
        
        settings.ali_tts_enabled = enabled
        settings.ali_tts_app_key = app_key
        settings.ali_tts_token = token
        
        try:
            tts_connector = TTSConnector()
            service_name = tts_connector.get_service_name()
            
            print(f"   Service name: {service_name}")
            
            if enabled and app_key and token:
                expected_service = "TTS Service (Ali Cloud)"
            else:
                expected_service = "TTS Service (Mock)"
            
            if service_name == expected_service:
                print(f"   ✅ Service selection correct")
                results.append(True)
            else:
                print(f"   ❌ Expected {expected_service}, got {service_name}")
                results.append(False)
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            results.append(False)
        finally:
            # 恢复配置
            settings.ali_tts_enabled = original_enabled
            settings.ali_tts_app_key = original_app_key
            settings.ali_tts_token = original_token
    
    return all(results)


async def main():
    """运行所有测试"""
    print("🧪 Ali TTS Integration Tests")
    print("=" * 50)
    
    tests = [
        ("Ali TTS Remote Service", test_ali_tts_service),
        ("TTS Connector Integration", test_tts_connector_integration),
        ("TTS Error Handling", test_tts_error_handling),
        ("Configuration Scenarios", test_configuration_scenarios)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} Test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 Test Summary")
    print("=" * 50)
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall Result: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        print("🎉 All Ali TTS integration tests PASSED!")
        print("✅ Ali TTS service is ready for production use")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        print("💡 Note: Tests may fail if Ali TTS credentials are not configured")


if __name__ == "__main__":
    asyncio.run(main())
