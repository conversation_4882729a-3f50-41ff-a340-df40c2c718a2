"""
测试音频保存功能
验证Base64音频数据能否正确保存为WAV文件
"""
import asyncio
import base64
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.connectors import ASRConnector


def create_test_pcm_data(duration_seconds: float = 1.0) -> bytes:
    """
    创建测试用的PCM音频数据
    生成一个简单的正弦波音频
    
    Args:
        duration_seconds: 音频时长（秒）
        
    Returns:
        bytes: PCM音频数据
    """
    import math
    
    sample_rate = 16000
    frequency = 440  # A4音符，440Hz
    samples = int(sample_rate * duration_seconds)
    
    pcm_data = bytearray()
    
    for i in range(samples):
        # 生成正弦波
        t = i / sample_rate
        amplitude = 0.3  # 音量（0-1）
        sample_value = int(amplitude * 32767 * math.sin(2 * math.pi * frequency * t))
        
        # 转换为16位小端序
        pcm_data.extend(sample_value.to_bytes(2, byteorder='little', signed=True))
    
    return bytes(pcm_data)


def create_test_base64_audio() -> str:
    """
    创建测试用的Base64编码音频数据
    
    Returns:
        str: Base64编码的音频数据
    """
    # 生成1秒的测试音频
    pcm_data = create_test_pcm_data(1.0)
    
    # 编码为Base64
    base64_data = base64.b64encode(pcm_data).decode('utf-8')
    
    return base64_data


async def test_audio_save_functionality():
    """测试音频保存功能"""
    print("🧪 Testing Audio Save Functionality")
    print("=" * 50)
    
    # 创建ASR连接器
    asr_connector = ASRConnector()
    
    try:
        # 1. 测试生成的音频数据
        print("📊 Test 1: Generated PCM Audio Data")
        test_audio_base64 = create_test_base64_audio()
        
        print(f"   Generated audio data length: {len(test_audio_base64)}")
        print(f"   Audio data preview: {test_audio_base64[:50]}...")
        
        # 调用ASR处理（会触发音频保存）
        result = await asr_connector.speech_to_text(
            audio_data=test_audio_base64,
            request_id="test-generated-audio"
        )
        
        print(f"   ✅ ASR Result: {result}")
        print()
        
        # 2. 测试实际的Base64音频数据（来自API测试）
        print("📊 Test 2: Real Base64 Audio Data")
        real_audio_base64 = "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT"
        
        print(f"   Real audio data length: {len(real_audio_base64)}")
        print(f"   Audio data preview: {real_audio_base64[:50]}...")
        
        result = await asr_connector.speech_to_text(
            audio_data=real_audio_base64,
            request_id="test-real-audio"
        )
        
        print(f"   ✅ ASR Result: {result}")
        print()
        
        # 3. 测试带前缀的Base64数据
        print("📊 Test 3: Base64 with Data URL Prefix")
        prefixed_audio = f"data:audio/wav;base64,{test_audio_base64}"
        
        print(f"   Prefixed audio data length: {len(prefixed_audio)}")
        print(f"   Audio data preview: {prefixed_audio[:50]}...")
        
        result = await asr_connector.speech_to_text(
            audio_data=prefixed_audio,
            request_id="test-prefixed-audio"
        )
        
        print(f"   ✅ ASR Result: {result}")
        print()
        
        # 4. 检查保存的文件
        print("📁 Checking Saved Audio Files")
        audio_dir = Path("audio_files")
        
        if audio_dir.exists():
            audio_files = list(audio_dir.glob("*.wav"))
            print(f"   Found {len(audio_files)} audio files:")
            
            for file_path in audio_files:
                file_size = file_path.stat().st_size
                print(f"   - {file_path.name} ({file_size} bytes)")
                
                # 验证WAV文件头
                with open(file_path, 'rb') as f:
                    header = f.read(44)
                    if len(header) >= 12 and header[:4] == b'RIFF' and header[8:12] == b'WAVE':
                        print(f"     ✅ Valid WAV file header")
                    else:
                        print(f"     ❌ Invalid WAV file header")
        else:
            print("   ❌ Audio directory not found")
        
        print("\n🎉 Audio save functionality test completed!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        await asr_connector.close()


async def test_wav_header_creation():
    """测试WAV文件头创建"""
    print("\n🧪 Testing WAV Header Creation")
    print("=" * 50)
    
    asr_connector = ASRConnector()
    
    try:
        # 测试不同大小的PCM数据
        test_sizes = [1000, 16000, 32000, 64000]  # 不同的PCM数据大小
        
        for pcm_size in test_sizes:
            header = asr_connector._create_wav_header(pcm_size)
            
            print(f"📊 PCM Size: {pcm_size} bytes")
            print(f"   Header Size: {len(header)} bytes")
            print(f"   Header Preview: {header[:12]}")
            
            # 验证WAV文件头格式
            if len(header) == 44 and header[:4] == b'RIFF' and header[8:12] == b'WAVE':
                print(f"   ✅ Valid WAV header")
            else:
                print(f"   ❌ Invalid WAV header")
            
            # 解析文件大小
            import struct
            file_size = struct.unpack('<I', header[4:8])[0]
            expected_size = 36 + pcm_size
            
            if file_size == expected_size:
                print(f"   ✅ Correct file size: {file_size}")
            else:
                print(f"   ❌ Incorrect file size: {file_size}, expected: {expected_size}")
            
            print()
            
    except Exception as e:
        print(f"❌ WAV header test failed: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        await asr_connector.close()


if __name__ == "__main__":
    async def main():
        # 测试音频保存功能
        await test_audio_save_functionality()
        
        # 测试WAV文件头创建
        await test_wav_header_creation()
        
        print("\n🏁 All audio tests completed!")
    
    asyncio.run(main())
