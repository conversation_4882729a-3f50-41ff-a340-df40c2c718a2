# 应用配置
APP_NAME=InxVision Voice Service
APP_VERSION=0.1.0
DEBUG=true

# 服务配置
HOST=0.0.0.0
PORT=8000

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 对话上下文配置
CONTEXT_EXPIRE_SECONDS=1800
MAX_CONTEXT_TURNS=10

# LLM配置 (需要配置真实的LLM服务)
LLM_API_URL=https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation
LLM_API_KEY=your_llm_api_key_here
LLM_MODEL=qwen-turbo
LLM_TIMEOUT=30

# ASR配置 (模拟阶段暂不使用)
ASR_API_URL=
ASR_API_KEY=

# 阿里云ASR配置
ALI_ASR_APP_KEY=your_ali_asr_app_key_here
ALI_ASR_TOKEN=your_ali_asr_token_here
ALI_ASR_ENABLED=false

# TTS配置 (模拟阶段暂不使用)
TTS_API_URL=
TTS_API_KEY=

# 阿里云TTS配置
ALI_TTS_APP_KEY=your_ali_tts_app_key_here
ALI_TTS_TOKEN=your_ali_tts_token_here
ALI_TTS_ENABLED=false
TTS_HTTP_SERVER_HOST=************

# Platform Service配置
PLATFORM_SERVICE_URL=http://localhost:8001
PLATFORM_SERVICE_TIMEOUT=10

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=text
