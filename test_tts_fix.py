"""
测试TTS修复
验证错误处理是否正常
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.connectors.tts_connector import TTSConnector
from src.connectors.base_connector import ConnectorError
from src.config import settings


async def test_tts_error_handling():
    """测试TTS错误处理"""
    print("🧪 Testing TTS Error Handling Fix")
    print("=" * 40)
    
    # 保存原始配置
    original_enabled = settings.ali_tts_enabled
    original_app_key = getattr(settings, 'ali_tts_app_key', '')
    original_token = getattr(settings, 'ali_tts_token', '')
    
    try:
        # 测试场景1：阿里云TTS未启用
        print("\n📋 Test 1: Ali TTS Disabled")
        settings.ali_tts_enabled = False
        
        tts_connector = TTSConnector()
        print(f"   Service name: {tts_connector.get_service_name()}")
        
        result = await tts_connector.text_to_speech(
            text="测试文本",
            voice_config={'voice': 'xiaoyun'},
            output_format='wav',
            return_base64=False,
            request_id='test-fix-001'
        )
        
        print(f"   ✅ Result: {result[:50]}..." if len(str(result)) > 50 else f"   ✅ Result: {result}")
        
        # 测试场景2：阿里云TTS启用但无效凭证
        print("\n📋 Test 2: Ali TTS Invalid Credentials")
        settings.ali_tts_enabled = True
        settings.ali_tts_app_key = "invalid_key"
        settings.ali_tts_token = "invalid_token"
        
        tts_connector2 = TTSConnector()
        print(f"   Service name: {tts_connector2.get_service_name()}")
        
        try:
            result2 = await tts_connector2.text_to_speech(
                text="测试错误处理",
                voice_config={'voice': 'xiaoyun'},
                output_format='wav',
                return_base64=False,
                request_id='test-fix-002'
            )
            print(f"   ❌ Expected exception but got result: {result2}")
            return False
            
        except ConnectorError as e:
            print(f"   ✅ Expected ConnectorError caught: {e.error_code}")
            print(f"   ✅ Error message: {str(e)[:100]}...")
            
        except Exception as e:
            print(f"   ❌ Unexpected exception: {type(e).__name__}: {e}")
            return False
        
        # 测试场景3：有效凭证（如果配置了）
        if hasattr(settings, 'ali_tts_app_key') and settings.ali_tts_app_key and settings.ali_tts_app_key != "invalid_key":
            print("\n📋 Test 3: Ali TTS Valid Credentials")
            # 恢复真实凭证进行测试
            settings.ali_tts_app_key = original_app_key
            settings.ali_tts_token = original_token
            
            tts_connector3 = TTSConnector()
            print(f"   Service name: {tts_connector3.get_service_name()}")
            
            try:
                result3 = await tts_connector3.text_to_speech(
                    text="真实TTS测试",
                    voice_config={'voice': 'xiaoyun', 'volume': 50},
                    output_format='wav',
                    return_base64=False,
                    request_id='test-fix-003'
                )
                print(f"   ✅ Real TTS result: {result3}")
                
            except Exception as e:
                print(f"   ⚠️  Real TTS failed (expected if no valid credentials): {e}")
        
        print("\n🎉 All error handling tests completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 恢复原始配置
        settings.ali_tts_enabled = original_enabled
        settings.ali_tts_app_key = original_app_key
        settings.ali_tts_token = original_token


async def test_voice_config_handling():
    """测试voice_config参数处理"""
    print("\n🧪 Testing Voice Config Handling")
    print("=" * 40)
    
    # 确保使用模拟模式
    original_enabled = settings.ali_tts_enabled
    settings.ali_tts_enabled = False
    
    try:
        tts_connector = TTSConnector()
        
        # 测试不同的voice_config格式
        test_configs = [
            {'voice': 'xiaoyun', 'volume': 50},
            {'voice': 'xiaogang'},
            'default',
            'friendly',
            None
        ]
        
        for i, config in enumerate(test_configs):
            print(f"\n📋 Test voice_config {i+1}: {config}")
            
            try:
                result = await tts_connector.text_to_speech(
                    text=f"测试语音配置{i+1}",
                    voice_config=config,
                    output_format='wav',
                    return_base64=False,
                    request_id=f'test-voice-{i+1}'
                )
                print(f"   ✅ Success: {result[:50]}..." if len(str(result)) > 50 else f"   ✅ Success: {result}")
                
            except Exception as e:
                print(f"   ❌ Failed: {e}")
                return False
        
        print("\n🎉 All voice config tests passed!")
        return True
        
    finally:
        settings.ali_tts_enabled = original_enabled


async def main():
    """运行所有测试"""
    print("🧪 TTS Fix Verification Tests")
    print("=" * 50)
    
    tests = [
        ("TTS Error Handling", test_tts_error_handling),
        ("Voice Config Handling", test_voice_config_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} Test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 Test Summary")
    print("=" * 50)
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall Result: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        print("🎉 All TTS fix tests PASSED!")
        print("✅ Error handling is working correctly")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")


if __name__ == "__main__":
    asyncio.run(main())
