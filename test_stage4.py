"""
阶段四功能验证测试 - Function Calling实现
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.tools import ToolManager, DeviceControlTool, DeviceQueryTool
from src.services import VoiceInteractionService
from src.models import (
    VoiceInteractRequest,
    AudioFormat,
    ExtendParams
)


async def test_tool_manager():
    """测试工具管理器功能"""
    print("🔧 Testing Tool Manager")
    print("=" * 50)
    
    manager = ToolManager()
    
    try:
        # 测试工具注册
        print("📋 Available Tools:")
        tools = manager.get_available_tools()
        for tool in tools:
            print(f"   - {tool}")
        
        # 测试工具定义
        print(f"\n📝 Tool Definitions:")
        definitions = manager.get_tool_definitions()
        for definition in definitions:
            func_info = definition["function"]
            print(f"   - {func_info['name']}: {func_info['description']}")
        
        # 测试单个工具调用
        print(f"\n🎯 Testing Device Control Tool:")
        result = await manager.execute_tool_call(
            tool_name="controlDevice",
            call_id="test-001",
            parameters={
                "deviceId": "light-01",
                "deviceName": "会议室灯光",
                "action": "turnOn"
            },
            context={
                "authorization": "Bearer test-token",
                "buildingId": "test-building",
                "permissions": {"canControl": True, "canQuery": True}
            }
        )
        
        print(f"   ✅ Success: {result.success}")
        print(f"   📝 Message: {result.message}")
        print(f"   ⏱️  Execution Time: {result.execution_time_ms}ms")
        print(f"   📊 Status: {result.status.value}")
        
        # 测试批量工具调用
        print(f"\n🔄 Testing Batch Tool Calls:")
        tool_calls = [
            {
                "id": "call_001",
                "function": {
                    "name": "controlDevice",
                    "arguments": {
                        "deviceId": "light-01",
                        "action": "turnOn",
                        "deviceName": "会议室灯光"
                    }
                }
            },
            {
                "id": "call_002",
                "function": {
                    "name": "controlDevice",
                    "arguments": {
                        "deviceId": "ac-01",
                        "action": "setTemperature",
                        "value": 24,
                        "deviceName": "会议室空调"
                    }
                }
            },
            {
                "id": "call_003",
                "function": {
                    "name": "queryDeviceStatus",
                    "arguments": {
                        "deviceId": "light-01",
                        "deviceName": "会议室灯光"
                    }
                }
            }
        ]
        
        batch_results = await manager.execute_tool_calls(
            tool_calls=tool_calls,
            context={
                "authorization": "Bearer test-token",
                "buildingId": "test-building",
                "permissions": {"canControl": True, "canQuery": True}
            }
        )
        
        successful_count = sum(1 for r in batch_results if r.success)
        print(f"   📊 Total calls: {len(batch_results)}")
        print(f"   ✅ Successful: {successful_count}")
        print(f"   ❌ Failed: {len(batch_results) - successful_count}")
        
        for i, result in enumerate(batch_results):
            print(f"   Call {i+1}: {result.tool_name} - {result.status.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tool Manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        await manager.close()


async def test_enhanced_voice_interaction():
    """测试增强的语音交互服务（集成Function Calling）"""
    print("\n🎤 Testing Enhanced Voice Interaction Service")
    print("=" * 50)
    
    service = VoiceInteractionService()
    
    try:
        # 测试设备控制场景
        print("🏠 Testing Device Control Scenario:")
        
        control_request = VoiceInteractRequest(
            requestId="stage4-control-001",
            token="stage4-control-token",
            timestamp=1703123456789,
            audioData="A" * 200,  # 长度200，触发设备控制场景
            audioFormat=AudioFormat(
                sampleRate=16000,
                channels=1,
                encoding="PCM_16BIT"
            ),
            extend=ExtendParams(
                authorization="Bearer stage4-control-token",
                buildingId="1942499471542292482"
            )
        )
        
        response = await service.process_voice_interaction(control_request)
        
        print(f"   ✅ Success: {response.success}")
        print(f"   📝 User Text: {response.data.text}")
        print(f"   💬 Response: {response.data.responseText}")
        print(f"   🎯 Type: {response.data.interactionType}")
        print(f"   ⏱️  Processing Time: {response.cost}ms")
        
        if response.data.deviceUpdates:
            print(f"   📱 Device Updates:")
            for update in response.data.deviceUpdates:
                print(f"      - {update.deviceName}: {update.properties}")
        
        # 测试查询场景
        print(f"\n🔍 Testing Device Query Scenario:")
        
        query_request = VoiceInteractRequest(
            requestId="stage4-query-001",
            token="stage4-query-token",
            timestamp=1703123456789,
            audioData="A" * 150,  # 长度150，触发查询场景
            audioFormat=AudioFormat(
                sampleRate=16000,
                channels=1,
                encoding="PCM_16BIT"
            ),
            extend=ExtendParams(
                authorization="Bearer stage4-query-token",
                buildingId="1942499471542292482"
            )
        )
        
        response = await service.process_voice_interaction(query_request)
        
        print(f"   ✅ Success: {response.success}")
        print(f"   📝 User Text: {response.data.text}")
        print(f"   💬 Response: {response.data.responseText}")
        print(f"   🎯 Type: {response.data.interactionType}")
        print(f"   ⏱️  Processing Time: {response.cost}ms")
        
        return True
        
    except Exception as e:
        print(f"❌ Enhanced voice interaction test failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        await service.close()


async def test_tool_parameter_validation():
    """测试工具参数验证"""
    print("\n🔍 Testing Tool Parameter Validation")
    print("=" * 50)
    
    manager = ToolManager()
    
    try:
        # 测试有效参数
        print("✅ Testing Valid Parameters:")
        valid_result = await manager.execute_tool_call(
            tool_name="controlDevice",
            call_id="valid-test-001",
            parameters={
                "deviceId": "light-01",
                "action": "setBrightness",
                "value": 75,
                "deviceName": "客厅灯光"
            },
            context={
                "authorization": "Bearer test-token",
                "buildingId": "test-building",
                "permissions": {"canControl": True, "canQuery": True}
            }
        )
        
        print(f"   Result: {valid_result.success} - {valid_result.message}")
        
        # 测试无效参数 - 缺少必需参数
        print(f"\n❌ Testing Invalid Parameters (Missing Required):")
        invalid_result1 = await manager.execute_tool_call(
            tool_name="controlDevice",
            call_id="invalid-test-001",
            parameters={
                "deviceName": "测试设备"
                # 缺少 deviceId 和 action
            },
            context={
                "authorization": "Bearer test-token",
                "buildingId": "test-building",
                "permissions": {"canControl": True, "canQuery": True}
            }
        )
        
        print(f"   Result: {invalid_result1.success} - {invalid_result1.message}")
        
        # 测试无效参数 - 亮度值超出范围
        print(f"\n❌ Testing Invalid Parameters (Out of Range):")
        invalid_result2 = await manager.execute_tool_call(
            tool_name="controlDevice",
            call_id="invalid-test-002",
            parameters={
                "deviceId": "light-01",
                "action": "setBrightness",
                "value": 150  # 超出0-100范围
            },
            context={
                "authorization": "Bearer test-token",
                "buildingId": "test-building",
                "permissions": {"canControl": True, "canQuery": True}
            }
        )
        
        print(f"   Result: {invalid_result2.success} - {invalid_result2.message}")
        
        # 测试权限不足
        print(f"\n🚫 Testing Permission Denied:")
        permission_result = await manager.execute_tool_call(
            tool_name="controlDevice",
            call_id="permission-test-001",
            parameters={
                "deviceId": "light-01",
                "action": "turnOn"
            },
            context={
                "authorization": "Bearer test-token",
                "buildingId": "test-building",
                "permissions": {"canControl": False, "canQuery": True}  # 无控制权限
            }
        )
        
        print(f"   Result: {permission_result.success} - {permission_result.message}")
        
        return True
        
    except Exception as e:
        print(f"❌ Parameter validation test failed: {e}")
        return False
        
    finally:
        await manager.close()


async def test_tool_performance():
    """测试工具执行性能"""
    print("\n⚡ Testing Tool Performance")
    print("=" * 50)
    
    manager = ToolManager()
    
    try:
        # 测试单个工具调用性能
        print("🚀 Single Tool Call Performance:")
        
        import time
        start_time = time.time()
        
        result = await manager.execute_tool_call(
            tool_name="controlDevice",
            call_id="perf-test-001",
            parameters={
                "deviceId": "light-01",
                "action": "turnOn",
                "deviceName": "性能测试灯光"
            },
            context={
                "authorization": "Bearer test-token",
                "buildingId": "test-building",
                "permissions": {"canControl": True, "canQuery": True}
            }
        )
        
        total_time = int((time.time() - start_time) * 1000)
        
        print(f"   ✅ Success: {result.success}")
        print(f"   ⏱️  Tool Execution Time: {result.execution_time_ms}ms")
        print(f"   ⏱️  Total Time: {total_time}ms")
        
        # 测试并发工具调用性能
        print(f"\n🔄 Concurrent Tool Calls Performance:")
        
        start_time = time.time()
        
        # 创建5个并发工具调用
        tool_calls = []
        for i in range(5):
            tool_calls.append({
                "id": f"perf_call_{i:03d}",
                "function": {
                    "name": "controlDevice",
                    "arguments": {
                        "deviceId": f"device-{i:02d}",
                        "action": "turnOn",
                        "deviceName": f"性能测试设备{i+1}"
                    }
                }
            })
        
        batch_results = await manager.execute_tool_calls(
            tool_calls=tool_calls,
            context={
                "authorization": "Bearer test-token",
                "buildingId": "test-building",
                "permissions": {"canControl": True, "canQuery": True}
            },
            max_concurrent=3
        )
        
        total_batch_time = int((time.time() - start_time) * 1000)
        successful_count = sum(1 for r in batch_results if r.success)
        avg_execution_time = sum(r.execution_time_ms for r in batch_results if r.execution_time_ms) / len(batch_results)
        
        print(f"   📊 Total calls: {len(batch_results)}")
        print(f"   ✅ Successful: {successful_count}")
        print(f"   ⏱️  Total batch time: {total_batch_time}ms")
        print(f"   ⏱️  Average execution time: {avg_execution_time:.1f}ms")
        print(f"   🚀 Throughput: {len(batch_results) / (total_batch_time / 1000):.1f} calls/sec")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        return False
        
    finally:
        await manager.close()


async def main():
    """运行所有阶段四测试"""
    print("🧪 Stage 4: Function Calling Implementation Tests")
    print("=" * 60)
    
    tests = [
        ("Tool Manager", test_tool_manager),
        ("Enhanced Voice Interaction", test_enhanced_voice_interaction),
        ("Parameter Validation", test_tool_parameter_validation),
        ("Performance", test_tool_performance)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} Test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 Test Summary")
    print("=" * 60)
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall Result: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        print("🎉 All Stage 4 tests PASSED! Function Calling implementation is complete!")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")


if __name__ == "__main__":
    asyncio.run(main())
