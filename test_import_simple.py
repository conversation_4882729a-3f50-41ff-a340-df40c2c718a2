"""
简单的导入测试
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing imports...")
    
    # 测试基础模块
    from src.config import settings
    print("✅ Config imported")
    
    from src.logger import get_logger
    print("✅ Logger imported")
    
    from src.models import VoiceInteractRequest
    print("✅ Models imported")
    
    # 测试连接器
    from src.connectors import ASRConnector
    print("✅ ASR Connector imported")
    
    from src.connectors import LLMConnector
    print("✅ LLM Connector imported")
    
    from src.connectors import TTSConnector
    print("✅ TTS Connector imported")
    
    from src.connectors import PlatformServiceConnector
    print("✅ Platform Service Connector imported")
    
    # 测试存储
    from src.storage import ContextManager
    print("✅ Context Manager imported")
    
    # 测试服务
    from src.services import VoiceInteractionService
    print("✅ Voice Interaction Service imported")
    
    print("\n🎉 All imports successful!")
    
except Exception as e:
    print(f"❌ Import failed: {e}")
    import traceback
    traceback.print_exc()
