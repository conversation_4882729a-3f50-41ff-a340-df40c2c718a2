# InxVision Voice Service 开发任务列表

基于《语音交互整体技术方案.md》和《阶段任务.md》，本项目采用PoC验证方式，重点验证AI编排逻辑。

## 🎯 项目目标

开发智能语音服务(Voice Service)，实现"接收请求 → 组装信息 → 调用LLM → 解析并执行工具 → 生成回复"的核心逻辑验证。

## 📋 开发任务分解

### 阶段一：项目基础搭建 (1-2天)

#### 1.1 项目初始化
- [x] 创建Python项目结构
- [x] 配置虚拟环境和依赖管理(requirements.txt)
- [x] 集成FastAPI框架
- [x] 配置项目基础目录结构
- [x] 添加基础配置管理(config.py)
- [x] 配置日志系统
- [x] 编写项目README.md

#### 1.2 核心接口骨架
- [x] 实现POST /api/v1/voice/interact接口骨架
- [x] 定义请求/响应数据模型(Pydantic)
- [x] 实现基础的请求验证和错误处理
- [x] 添加API文档(FastAPI自动生成)
- [x] 配置CORS和中间件

### 阶段二：AI服务集成层 (3-4天)

#### 2.1 外部服务连接器基础
- [x] 创建Connector基类和接口定义
- [x] 实现HTTP客户端封装(HTTPX)
- [x] 配置连接池和超时设置
- [x] 实现统一的错误处理机制

#### 2.2 ASR连接器(模拟实现)
- [x] 创建ASRConnector类
- [x] 实现模拟ASR功能(返回固定文本)
- [x] 支持多种测试场景的文本返回
- [x] 添加音频格式验证(为后期真实集成准备)

#### 2.3 LLM连接器(真实实现)
- [x] 创建LLMConnector类
- [x] 集成选定的LLM API(如通义千问/文心一言)
- [x] 实现Function Calling工具定义
- [x] 实现Prompt模板管理
- [x] 实现两阶段LLM调用逻辑
- [x] 添加LLM响应解析和验证
- [x] 实现错误重试机制

#### 2.4 TTS连接器(模拟实现)
- [x] 创建TTSConnector类
- [x] 实现模拟TTS功能(返回文本，不生成音频)
- [x] 为后期真实TTS集成预留接口

#### 2.5 平台服务连接器(模拟实现)
- [x] 创建PlatformServiceConnector类
- [x] 模拟实现GET /api/v1/ai/context接口调用
- [x] 模拟实现POST /api/v1/ai/control接口调用
- [x] 返回符合API契约的模拟数据
- [x] 实现认证头透传逻辑

### 阶段三：核心业务逻辑 (2-3天) ✅

#### 3.1 交互编排服务 ✅
- [x] 创建VoiceInteractionService类
- [x] 实现完整的语音交互工作流
- [x] 集成所有Connector组件
- [x] 实现异步处理逻辑
- [x] 添加执行时间统计

#### 3.2 对话上下文管理 ✅
- [x] 集成Redis客户端
- [x] 实现会话上下文存储和读取
- [x] 实现上下文过期策略
- [x] 实现上下文数据结构管理
- [x] 添加上下文清理机制

#### 3.3 响应数据组装 ✅
- [x] 实现响应数据结构组装
- [x] 支持多种交互类型(result/confirmation/clarification等)
- [x] 实现UI组件数据生成
- [x] 实现设备状态更新数据生成
- [x] 实现多屏幕尺寸适配逻辑

### 阶段四：Function Calling实现 (2-3天) ✅

#### 4.1 工具定义管理 ✅
- [x] 定义设备控制工具集
- [x] 实现工具参数验证
- [x] 创建工具执行器基类
- [x] 实现工具执行结果标准化

#### 4.2 设备控制工具 ✅
- [x] 实现controlDevice工具
- [x] 实现queryDeviceStatus工具
- [x] 实现批量设备操作工具
- [x] 添加设备操作权限验证

#### 4.3 LLM工具调用集成 ✅
- [x] 实现工具调用请求组装
- [x] 实现工具调用结果解析
- [x] 实现工具执行错误处理
- [x] 实现工具调用链管理

### 阶段五：测试和验证 (2-3天)

#### 5.1 单元测试
- [ ] 为各个Connector编写单元测试
- [ ] 为核心业务逻辑编写单元测试
- [ ] 为工具调用逻辑编写单元测试
- [ ] 配置测试覆盖率检查

#### 5.2 集成测试
- [ ] 编写端到端API测试
- [ ] 测试各种语音交互场景
- [ ] 测试错误处理和异常情况
- [ ] 测试并发请求处理

#### 5.3 性能测试
- [ ] 测试接口响应时间
- [ ] 测试并发处理能力
- [ ] 测试内存使用情况
- [ ] 优化性能瓶颈

### 阶段六：部署准备 (1-2天)

#### 6.1 配置管理
- [ ] 完善环境变量配置
- [ ] 创建不同环境的配置文件
- [ ] 实现配置验证机制
- [ ] 添加敏感信息保护

#### 6.2 容器化
- [ ] 编写Dockerfile
- [ ] 配置docker-compose(包含Redis)
- [ ] 优化镜像大小
- [ ] 测试容器部署

#### 6.3 文档完善
- [ ] 完善API接口文档
- [ ] 编写部署说明文档
- [ ] 编写开发者指南
- [ ] 更新项目README

## 🔧 技术栈确认

- **开发语言**: Python 3.10+
- **Web框架**: FastAPI
- **HTTP客户端**: HTTPX
- **数据验证**: Pydantic
- **缓存**: Redis
- **测试框架**: pytest
- **容器化**: Docker

## 📊 里程碑计划

- **Week 1**: 完成阶段一、二 (项目搭建 + AI服务集成)
- **Week 2**: 完成阶段三、四 (核心逻辑 + Function Calling)
- **Week 3**: 完成阶段五、六 (测试验证 + 部署准备)

## 🎯 验证目标

最终能够实现：
1. 接收符合API契约的语音交互请求
2. 模拟ASR识别用户语音为文本
3. 真实调用LLM进行意图识别和工具调用
4. 模拟执行设备控制操作
5. 生成符合API契约的完整响应
6. 验证整个AI编排逻辑的可行性

## 📝 注意事项

1. **API契约优先**: 严格按照《语音交互整体技术方案.md》中的接口定义实现
2. **模拟策略**: ASR、TTS、Platform Service使用模拟实现，LLM使用真实服务
3. **错误处理**: 重点关注各种异常情况的处理
4. **日志记录**: 详细记录每个步骤的执行情况，便于调试
5. **配置灵活**: 支持通过配置切换模拟/真实服务
