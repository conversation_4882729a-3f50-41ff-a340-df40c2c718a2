"""
测试voice_config参数修复
验证字符串和字典格式的voice_config都能正常工作
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.connectors.tts_connector import TTSConnector
from src.config import settings


async def test_voice_config_formats():
    """测试不同格式的voice_config参数"""
    print("🧪 Testing Voice Config Formats")
    print("=" * 40)
    
    # 保存原始配置
    original_enabled = settings.ali_tts_enabled
    original_app_key = getattr(settings, 'ali_tts_app_key', '')
    original_token = getattr(settings, 'ali_tts_token', '')
    
    try:
        # 测试场景1：使用模拟TTS（避免真实API调用）
        print("\n📋 Test with Mock TTS Service")
        settings.ali_tts_enabled = False
        
        tts_connector = TTSConnector()
        print(f"   Service name: {tts_connector.get_service_name()}")
        
        # 测试不同的voice_config格式
        test_configs = [
            # 字典格式
            {'voice': 'xiaoyun', 'volume': 50},
            {'voice': 'xiaogang', 'volume': 60, 'speech_rate': 100},
            {'voice': 'xiaomeng'},
            
            # 字符串格式
            'xiaoyun',
            'xiaogang',
            'default',
            'friendly',
            
            # 边界情况
            None,
            {},
            'unknown_voice'
        ]
        
        for i, config in enumerate(test_configs):
            print(f"\n   Test {i+1}: voice_config = {config}")
            
            try:
                result = await tts_connector.text_to_speech(
                    text=f"测试语音配置{i+1}",
                    voice_config=config,
                    output_format='wav',
                    return_base64=False,
                    request_id=f'test-config-{i+1}'
                )
                
                print(f"      ✅ Success: {result[:50]}..." if len(str(result)) > 50 else f"      ✅ Success: {result}")
                
            except Exception as e:
                print(f"      ❌ Failed: {type(e).__name__}: {e}")
                return False
        
        # 测试场景2：使用阿里云TTS（如果配置了有效凭证）
        if original_app_key and original_token and original_app_key != "invalid_key":
            print(f"\n📋 Test with Ali TTS Service (Real API)")
            settings.ali_tts_enabled = True
            settings.ali_tts_app_key = original_app_key
            settings.ali_tts_token = original_token
            
            tts_connector2 = TTSConnector()
            print(f"   Service name: {tts_connector2.get_service_name()}")
            
            # 测试字典和字符串格式
            real_test_configs = [
                {'voice': 'xiaoyun', 'volume': 50},
                'xiaoyun'
            ]
            
            for i, config in enumerate(real_test_configs):
                print(f"\n   Real Test {i+1}: voice_config = {config}")
                
                try:
                    result = await tts_connector2.text_to_speech(
                        text=f"真实TTS测试{i+1}",
                        voice_config=config,
                        output_format='wav',
                        return_base64=False,
                        request_id=f'test-real-{i+1}'
                    )
                    
                    print(f"      ✅ Real TTS Success: {result}")
                    
                except Exception as e:
                    print(f"      ⚠️  Real TTS Failed (may be expected): {type(e).__name__}: {e}")
        
        print("\n🎉 All voice config format tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 恢复原始配置
        settings.ali_tts_enabled = original_enabled
        settings.ali_tts_app_key = original_app_key
        settings.ali_tts_token = original_token


async def test_base64_output():
    """测试Base64输出格式"""
    print("\n🧪 Testing Base64 Output Format")
    print("=" * 40)
    
    # 使用模拟TTS
    settings.ali_tts_enabled = False
    
    try:
        tts_connector = TTSConnector()
        
        # 测试Base64输出
        test_configs = [
            {'voice': 'xiaoyun', 'volume': 50},
            'xiaoyun',
            'default'
        ]
        
        for i, config in enumerate(test_configs):
            print(f"\n   Base64 Test {i+1}: voice_config = {config}")
            
            try:
                result = await tts_connector.text_to_speech(
                    text=f"Base64测试{i+1}",
                    voice_config=config,
                    output_format='mp3',
                    return_base64=True,
                    request_id=f'test-base64-{i+1}'
                )
                
                if isinstance(result, str) and len(result) > 100:
                    print(f"      ✅ Base64 Success: {len(result)} characters")
                else:
                    print(f"      ❌ Base64 Failed: Invalid result {result}")
                    return False
                    
            except Exception as e:
                print(f"      ❌ Base64 Failed: {type(e).__name__}: {e}")
                return False
        
        print("\n🎉 All Base64 output tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Base64 test failed: {e}")
        return False


async def main():
    """运行所有测试"""
    print("🧪 Voice Config Fix Verification Tests")
    print("=" * 50)
    
    tests = [
        ("Voice Config Formats", test_voice_config_formats),
        ("Base64 Output", test_base64_output)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} Test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 Test Summary")
    print("=" * 50)
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall Result: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        print("🎉 All voice config fix tests PASSED!")
        print("✅ Voice config parameter handling is working correctly")
        print("✅ Both dictionary and string formats are supported")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")


if __name__ == "__main__":
    asyncio.run(main())
