"""
阿里云ASR服务测试脚本
"""
import asyncio
import sys
import os
import base64

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.connectors.asr_ali_remote_service import AliASRRemoteService
from src.connectors.asr_connector import ASRConnector
from src.config import settings


async def test_ali_asr_service():
    """测试阿里云ASR服务"""
    print("🧪 Testing Ali ASR Service")
    print("=" * 50)
    
    # 创建阿里云ASR服务实例
    ali_asr = AliASRRemoteService()
    
    # 检查配置
    print("📋 Configuration Check:")
    config_info = ali_asr.get_config_info()
    for key, value in config_info.items():
        print(f"   {key}: {value}")
    
    if not ali_asr.is_configured():
        print("\n⚠️  Ali ASR service is not configured")
        print("   Please set ALI_ASR_APP_KEY and ALI_ASR_TOKEN in environment variables")
        print("   or update src/config.py with your credentials")
        return False
    
    # 测试音频文件（如果存在）
    audio_files = []
    audio_dir = "audio_files"
    if os.path.exists(audio_dir):
        audio_files = [f for f in os.listdir(audio_dir) if f.endswith('.wav')][:3]  # 最多测试3个文件
    
    if audio_files:
        print(f"\n🎵 Testing with audio files:")
        for audio_file in audio_files:
            file_path = os.path.join(audio_dir, audio_file)
            print(f"\n   Testing file: {audio_file}")
            
            try:
                result = await ali_asr.recognize_from_file(file_path)
                
                print(f"   ✅ Success: {result['success']}")
                if result['success']:
                    print(f"   📝 Text: {result['text']}")
                    print(f"   📊 Status: {result['status']}")
                else:
                    print(f"   ❌ Error: {result['error']}")
                    
            except Exception as e:
                print(f"   ❌ Exception: {e}")
    else:
        print("\n📁 No audio files found in audio_files directory")
    
    # 测试Base64数据（模拟数据）
    print(f"\n🔤 Testing with Base64 data:")
    
    # 创建一个简单的PCM音频数据（静音）
    sample_rate = 16000
    duration = 1  # 1秒
    samples = sample_rate * duration
    pcm_data = b'\x00\x00' * samples  # 16-bit静音数据
    
    base64_data = base64.b64encode(pcm_data).decode('utf-8')
    
    try:
        result = await ali_asr.recognize_from_base64(base64_data)
        
        print(f"   ✅ Success: {result['success']}")
        if result['success']:
            print(f"   📝 Text: {result['text']}")
            print(f"   📊 Status: {result['status']}")
        else:
            print(f"   ❌ Error: {result['error']}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    return True


async def test_asr_connector_integration():
    """测试ASR连接器集成"""
    print("\n🔌 Testing ASR Connector Integration")
    print("=" * 50)
    
    # 创建ASR连接器
    asr_connector = ASRConnector()
    
    # 检查阿里云ASR服务状态
    print("📋 Ali ASR Service Status:")
    print(f"   Configured: {asr_connector.ali_asr_service.is_configured()}")
    print(f"   Enabled: {settings.ali_asr_enabled}")
    
    # 创建测试音频数据
    print(f"\n🎵 Testing ASR Connector:")
    
    # 创建一个简单的PCM音频数据
    sample_rate = 16000
    duration = 2  # 2秒
    samples = sample_rate * duration
    pcm_data = b'\x00\x00' * samples  # 16-bit静音数据
    
    base64_data = base64.b64encode(pcm_data).decode('utf-8')
    
    try:
        # 测试语音转文本
        result_text = await asr_connector.speech_to_text(
            audio_data=base64_data,
            audio_format={
                'sampleRate': 16000,
                'channels': 1,
                'encoding': 'PCM_16BIT'
            },
            request_id='test-ali-asr-001'
        )
        
        print(f"   ✅ ASR Result: {result_text}")
        
        # 检查服务类型
        if settings.ali_asr_enabled and asr_connector.ali_asr_service.is_configured():
            print(f"   🌐 Service: Ali ASR (Real)")
        else:
            print(f"   🎭 Service: Mock ASR")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        import traceback
        traceback.print_exc()
    
    # 清理资源
    await asr_connector.close()
    
    return True


async def test_configuration_scenarios():
    """测试不同配置场景"""
    print("\n⚙️  Testing Configuration Scenarios")
    print("=" * 50)
    
    # 场景1：阿里云ASR未配置
    print("📋 Scenario 1: Ali ASR not configured")
    ali_asr_unconfigured = AliASRRemoteService(app_key="", token="")
    print(f"   Configured: {ali_asr_unconfigured.is_configured()}")
    
    # 场景2：阿里云ASR已配置但未启用
    print(f"\n📋 Scenario 2: Ali ASR configured but disabled")
    original_enabled = settings.ali_asr_enabled
    settings.ali_asr_enabled = False
    
    asr_connector = ASRConnector()
    print(f"   Enabled: {settings.ali_asr_enabled}")
    print(f"   Configured: {asr_connector.ali_asr_service.is_configured()}")
    
    # 恢复原始设置
    settings.ali_asr_enabled = original_enabled
    await asr_connector.close()
    
    # 场景3：阿里云ASR已配置且已启用
    print(f"\n📋 Scenario 3: Ali ASR configured and enabled")
    print(f"   Enabled: {settings.ali_asr_enabled}")
    
    ali_asr_configured = AliASRRemoteService()
    print(f"   Configured: {ali_asr_configured.is_configured()}")
    
    return True


async def main():
    """运行所有测试"""
    print("🧪 Ali ASR Service Tests")
    print("=" * 60)
    
    tests = [
        ("Ali ASR Service", test_ali_asr_service),
        ("ASR Connector Integration", test_asr_connector_integration),
        ("Configuration Scenarios", test_configuration_scenarios)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} Test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 Test Summary")
    print("=" * 60)
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall Result: {passed_count}/{total_count} tests passed")
    
    # 配置提示
    print(f"\n💡 Configuration Tips:")
    print(f"   1. Set ALI_ASR_ENABLED=true in environment or config")
    print(f"   2. Set ALI_ASR_APP_KEY=your_app_key")
    print(f"   3. Set ALI_ASR_TOKEN=your_token")
    print(f"   4. Ensure audio files are in PCM format, 16kHz, 16-bit")


if __name__ == "__main__":
    asyncio.run(main())
