"""
测试ASR异常处理逻辑
验证阿里云ASR失败时的异常抛出机制
"""
import asyncio
import sys
import os
import base64

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.connectors.asr_connector import ASRConnector
from src.connectors.base_connector import ConnectorError
from src.config import settings


async def test_ali_asr_disabled():
    """测试阿里云ASR未启用时的行为"""
    print("🧪 Test 1: Ali ASR Disabled")
    print("-" * 40)
    
    # 确保阿里云ASR未启用
    original_enabled = settings.ali_asr_enabled
    settings.ali_asr_enabled = False
    
    try:
        asr_connector = ASRConnector()
        
        # 创建测试音频数据
        sample_rate = 16000
        duration = 1
        samples = sample_rate * duration
        pcm_data = b'\x00\x00' * samples
        base64_data = base64.b64encode(pcm_data).decode('utf-8')
        
        # 测试语音转文本
        result = await asr_connector.speech_to_text(
            audio_data=base64_data,
            audio_format={'sampleRate': 16000, 'channels': 1, 'encoding': 'PCM_16BIT'},
            request_id='test-disabled-001'
        )
        
        print(f"✅ Result: {result}")
        print(f"✅ Service: {asr_connector.get_service_name()}")
        print("✅ Test passed: Mock ASR service used as expected")
        
        await asr_connector.close()
        return True
        
    except Exception as e:
        print(f"❌ Unexpected exception: {e}")
        return False
    finally:
        settings.ali_asr_enabled = original_enabled


async def test_ali_asr_enabled_but_not_configured():
    """测试阿里云ASR启用但未配置时的行为"""
    print("\n🧪 Test 2: Ali ASR Enabled but Not Configured")
    print("-" * 50)
    
    # 启用阿里云ASR但不配置凭证
    original_enabled = settings.ali_asr_enabled
    original_app_key = settings.ali_asr_app_key
    original_token = settings.ali_asr_token
    
    settings.ali_asr_enabled = True
    settings.ali_asr_app_key = ""
    settings.ali_asr_token = ""
    
    try:
        asr_connector = ASRConnector()
        
        # 创建测试音频数据
        sample_rate = 16000
        duration = 1
        samples = sample_rate * duration
        pcm_data = b'\x00\x00' * samples
        base64_data = base64.b64encode(pcm_data).decode('utf-8')
        
        # 测试语音转文本
        result = await asr_connector.speech_to_text(
            audio_data=base64_data,
            audio_format={'sampleRate': 16000, 'channels': 1, 'encoding': 'PCM_16BIT'},
            request_id='test-not-configured-001'
        )
        
        print(f"✅ Result: {result}")
        print(f"✅ Service: {asr_connector.get_service_name()}")
        print("✅ Test passed: Mock ASR service used (Ali ASR not configured)")
        
        await asr_connector.close()
        return True
        
    except Exception as e:
        print(f"❌ Unexpected exception: {e}")
        return False
    finally:
        settings.ali_asr_enabled = original_enabled
        settings.ali_asr_app_key = original_app_key
        settings.ali_asr_token = original_token


async def test_ali_asr_enabled_and_configured():
    """测试阿里云ASR启用且配置时的行为（会失败因为凭证无效）"""
    print("\n🧪 Test 3: Ali ASR Enabled and Configured (Invalid Credentials)")
    print("-" * 65)
    
    # 启用阿里云ASR并配置无效凭证
    original_enabled = settings.ali_asr_enabled
    original_app_key = settings.ali_asr_app_key
    original_token = settings.ali_asr_token
    
    settings.ali_asr_enabled = True
    settings.ali_asr_app_key = "test_invalid_app_key"
    settings.ali_asr_token = "test_invalid_token"
    
    try:
        asr_connector = ASRConnector()
        
        print(f"📋 Service configured: {asr_connector.ali_asr_service.is_configured()}")
        print(f"📋 Service enabled: {settings.ali_asr_enabled}")
        print(f"📋 Service name: {asr_connector.get_service_name()}")
        
        # 创建测试音频数据
        sample_rate = 16000
        duration = 1
        samples = sample_rate * duration
        pcm_data = b'\x00\x00' * samples
        base64_data = base64.b64encode(pcm_data).decode('utf-8')
        
        # 测试语音转文本（应该抛出异常）
        result = await asr_connector.speech_to_text(
            audio_data=base64_data,
            audio_format={'sampleRate': 16000, 'channels': 1, 'encoding': 'PCM_16BIT'},
            request_id='test-configured-001'
        )
        
        print(f"❌ Unexpected success: {result}")
        print("❌ Test failed: Expected exception but got result")
        
        await asr_connector.close()
        return False
        
    except ConnectorError as e:
        print(f"✅ Expected ConnectorError caught: {e}")
        print(f"✅ Error code: {e.error_code}")
        print("✅ Test passed: Ali ASR failure properly handled with exception")
        return True
        
    except Exception as e:
        print(f"❌ Unexpected exception type: {type(e).__name__}: {e}")
        return False
    finally:
        settings.ali_asr_enabled = original_enabled
        settings.ali_asr_app_key = original_app_key
        settings.ali_asr_token = original_token


async def test_voice_interaction_service_exception_handling():
    """测试语音交互服务的异常处理"""
    print("\n🧪 Test 4: Voice Interaction Service Exception Handling")
    print("-" * 55)
    
    from src.services import VoiceInteractionService
    from src.models import VoiceInteractRequest, AudioFormat, ExtendParams
    
    # 启用阿里云ASR并配置无效凭证
    original_enabled = settings.ali_asr_enabled
    original_app_key = settings.ali_asr_app_key
    original_token = settings.ali_asr_token
    
    settings.ali_asr_enabled = True
    settings.ali_asr_app_key = "test_invalid_app_key"
    settings.ali_asr_token = "test_invalid_token"
    
    try:
        service = VoiceInteractionService()
        
        # 创建测试请求
        request = VoiceInteractRequest(
            requestId="test-exception-001",
            token="test-exception-token",
            timestamp=1703123456789,
            audioData="A" * 200,  # 长度200
            audioFormat=AudioFormat(
                sampleRate=16000,
                channels=1,
                encoding="PCM_16BIT"
            ),
            extend=ExtendParams(
                authorization="Bearer test-exception-token",
                buildingId="1942499471542292482"
            )
        )
        
        # 处理请求（应该在ASR步骤失败）
        response = await service.process_voice_interaction(request)
        
        print(f"📊 Response success: {response.success}")
        print(f"📊 Response message: {response.message}")
        print(f"📊 Response data: {response.data}")
        
        if not response.success:
            print("✅ Test passed: Service properly handled ASR exception")
            result = True
        else:
            print("❌ Test failed: Expected failure but got success")
            result = False
        
        await service.close()
        return result
        
    except Exception as e:
        print(f"❌ Service exception: {type(e).__name__}: {e}")
        return False
    finally:
        settings.ali_asr_enabled = original_enabled
        settings.ali_asr_app_key = original_app_key
        settings.ali_asr_token = original_token


async def main():
    """运行所有异常处理测试"""
    print("🧪 ASR Exception Handling Tests")
    print("=" * 60)
    
    tests = [
        ("Ali ASR Disabled", test_ali_asr_disabled),
        ("Ali ASR Not Configured", test_ali_asr_enabled_but_not_configured),
        ("Ali ASR Invalid Credentials", test_ali_asr_enabled_and_configured),
        ("Voice Service Exception Handling", test_voice_interaction_service_exception_handling)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name} Test...")
        try:
            result = await test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} test PASSED")
            else:
                print(f"❌ {test_name} test FAILED")
        except Exception as e:
            print(f"❌ {test_name} test FAILED with exception: {e}")
            import traceback
            traceback.print_exc()
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 Test Summary")
    print("=" * 60)
    
    passed_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 Overall Result: {passed_count}/{total_count} tests passed")
    
    if passed_count == total_count:
        print("🎉 All exception handling tests PASSED!")
        print("✅ Ali ASR failures now properly throw exceptions")
        print("✅ No more fallback to mock ASR when Ali ASR fails")
    else:
        print("⚠️  Some tests failed. Please review the implementation.")


if __name__ == "__main__":
    asyncio.run(main())
