"""
API测试脚本
测试语音交互接口是否正常工作
"""
import requests
import json
import time


def test_voice_interact_api():
    """测试语音交互API"""
    
    # API地址
    url = "http://localhost:6090/api/v1/voice/interact"
    
    # 测试请求数据
    test_data = {
        "requestId": "550e8400-e29b-41d4-a716-446655440000",
        "token": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0Y",
        "timestamp": 1703123456789,
        "audioData": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT",
        "audioFormat": {
            "sampleRate": 16000,
            "channels": 1,
            "encoding": "PCM_16BIT"
        },
        "extend": {
            "authorization": "bearer 6dd7cc7a-f44e-45cc-ba5b-c95ef2e95dc5",
            "buildingId": "1942499471542292482"
        }
    }
    
    print("🧪 Testing Voice Interaction API")
    print("=" * 50)
    print(f"📍 URL: {url}")
    print(f"📝 Request Data:")
    print(json.dumps(test_data, indent=2, ensure_ascii=False))
    print()
    
    try:
        # 发送请求
        print("🚀 Sending request...")
        start_time = time.time()
        
        response = requests.post(
            url,
            json=test_data,
            headers={
                "Content-Type": "application/json"
            },
            timeout=60  # 60秒超时
        )
        
        end_time = time.time()
        request_time = int((end_time - start_time) * 1000)
        
        print(f"⏱️  Request completed in {request_time}ms")
        print(f"📊 Status Code: {response.status_code}")
        print()
        
        # 检查响应
        if response.status_code == 200:
            print("✅ Request successful!")
            response_data = response.json()
            
            print("📋 Response Data:")
            print(json.dumps(response_data, indent=2, ensure_ascii=False))
            print()
            
            # 验证响应结构
            print("🔍 Response Validation:")
            assert "requestId" in response_data, "Missing requestId"
            assert "success" in response_data, "Missing success"
            assert "data" in response_data, "Missing data"
            assert response_data["requestId"] == test_data["requestId"], "RequestId mismatch"
            
            if response_data["success"]:
                data = response_data["data"]
                assert "text" in data, "Missing text in data"
                assert "responseText" in data, "Missing responseText in data"
                assert "interactionType" in data, "Missing interactionType in data"
                
                print(f"   ✅ Request ID: {response_data['requestId']}")
                print(f"   ✅ Success: {response_data['success']}")
                print(f"   ✅ User Text: {data.get('text', 'N/A')}")
                print(f"   ✅ Response Text: {data.get('responseText', 'N/A')}")
                print(f"   ✅ Interaction Type: {data.get('interactionType', 'N/A')}")
                print(f"   ✅ Processing Time: {response_data.get('cost', 'N/A')}ms")
                
                if data.get("audioUrl"):
                    print(f"   ✅ Audio URL: {data['audioUrl']}")
                
                if data.get("ui"):
                    print(f"   ✅ UI Component: {data['ui'].get('type', 'N/A')}")
                
                print("\n🎉 API test PASSED!")
                return True
            else:
                print(f"   ❌ Request failed: {response_data.get('msg', 'Unknown error')}")
                return False
                
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print("📋 Error Response:")
            try:
                error_data = response.json()
                print(json.dumps(error_data, indent=2, ensure_ascii=False))
            except:
                print(response.text)
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timeout (60s)")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running?")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False


def test_health_check():
    """测试健康检查接口"""
    
    url = "http://localhost:6090/health"
    
    print("\n🏥 Testing Health Check API")
    print("=" * 50)
    
    try:
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            print("✅ Health check passed!")
            print(f"📋 Response: {response.json()}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False


def test_api_docs():
    """测试API文档是否可访问"""
    
    url = "http://localhost:6090/docs"
    
    print("\n📖 Testing API Documentation")
    print("=" * 50)
    
    try:
        response = requests.get(url, timeout=5)
        
        if response.status_code == 200:
            print("✅ API documentation is accessible!")
            print(f"📍 Visit: {url}")
            return True
        else:
            print(f"❌ API docs not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API docs error: {e}")
        return False


if __name__ == "__main__":
    print("🧪 InxVision Voice Service API Testing")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ Waiting for service to be ready...")
    time.sleep(2)
    
    # 运行测试
    tests = [
        ("Health Check", test_health_check),
        ("API Documentation", test_api_docs),
        ("Voice Interaction API", test_voice_interact_api)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The API is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
