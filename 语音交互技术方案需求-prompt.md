帮我编写一个完整的物联网场景人机语音交互实现的技术方案，相关信息如下：

# 场景

物联网机电管理，主要用于园区、商业楼宇、办公室等区域的设备智能控制

# 任务

需要设计一个人机语音交互实现的完整技术方案，方便我后续知道怎么开发和实现

# 背景：

- 已经有成熟的物联网机电管理的平台系统，PC端实现了完整的设备接入、设备数据采集、设备控制、设备列表查看、设备归类、设备配置（物模型、设备类、设备实例、设备属性点位、属性事件等等），以及面向业务使用的业务系统（机电管理、能碳统计报表、空间场景智能控制）

- 部署架构 N个网关 接入平台，平台之上 有web端，有N个屏端（10寸屏/4寸屏），举例：某个办公楼场景，部署一个平台（含业务应用）服务器，所有的区域设备接入多个物联网关，多个物联网关接入平台，然后按空间需要，大楼每层楼都部署3~5个 10寸屏（在墙上）或者 4寸屏（在办公桌上），屏对接业务系统或者平台接口；

- 已经有屏端的基本功能，手指触摸操作，比如设置设备联动、定时开关、实时查看和操作设备（如照明、空调），每个屏由账号登录，账号在平台/业务系统创建并绑定空间（空间从大到小分多级，比如楼层-区域-子区域），区域会关联相关设备，所以某个固定账号会分配管理固定区域的设备，账号登录到屏端应用，就能看到哪些空间区域，以及这些空间区域下面的设备；

- 已经技术调研，能够实现在屏端热词唤醒、VAD检测，并请求到服务端处理，由于技术有限，屏端主要业务功能实现采用H5开发，andriod应用使用webview加载H5，热词唤醒和VAD检测由Android原生应用+JNDI实现，和H5联动采用JSbridge实现，均已准备好；

# 任务细化说明

- 初步设计思路是，屏端热词唤醒后，VAD检测用户自然语言录音，完成后转到 服务端，服务端进行ASR转文本，然后调用LLM大模型进行推理，应用根据LLM结果判断是否控制设备，如果需要控制设备则根据推理结果参数下发指令控制设备，最后把结果 进行TTS转语音（可选），最后返回给屏端，屏端H5展示结果或者播放结果语音

- 现在需要你细化整体技术方案（热词唤醒和VAD检测部分不需要了，已经调研实现）

# 要求

- 技术方案包括整体链路、架构、系统交互流程、关键点实现，如果有多种方案，可以根据背景判断哪种合适；

- 技术点上需要考虑多种技术点，比如ASR、TTS、LLM推理、Prompt管理和优化、同一屏端的对话上下文管理、Agent、MCP等相关技术；

- 技术设计需充分考虑用户和屏端的交互，比如用户提出需求（操作什么），接下来的设备操作反馈是否让用户再次确认才真正下发控制设备，设备控制结果怎么反馈给用户等等

- 由于4寸屏显示有效，场景可能不显示文字结果，直接播放语音操作结果，但是设备的状态结果可能还是需要参数返回（界面上有些设备状态可能需要更新）

- 10寸屏有人机对话界面，界面对话可能要显示一些对话内容，比如反馈用户确认信息时有多个选项卡片显示出来，让用户选择确认，比如操作结果片卡显示；

- 对于LLM推理部分，可能需要当前屏对话的上下文信息输入，当前屏所属空间、设备列表信息等输入、设备操作接口信息输入（function call好还是mcp好？）

- 请用中文回复，输出比较完整的md文档
