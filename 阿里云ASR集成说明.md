# 阿里云ASR集成说明

## 📋 概述

本项目已集成阿里云语音识别（ASR）服务，支持真实的语音转文本功能。系统采用**智能回退机制**：优先使用阿里云ASR服务，如果服务不可用或配置不正确，自动回退到模拟ASR服务。

## 🏗️ 架构设计

```
ASRConnector (ASR连接器)
├── AliASRRemoteService (阿里云ASR服务)
│   ├── 支持文件输入
│   ├── 支持Base64数据输入
│   ├── 支持直接音频数据输入
│   └── 异步HTTP请求
└── Mock ASR Service (模拟ASR服务)
    └── 智能测试场景选择
```

## 🔧 核心文件

### 1. `src/connectors/asr_ali_remote_service.py`
阿里云ASR远程服务实现：
- ✅ **多种输入方式**：文件路径、Base64数据、直接音频数据
- ✅ **异步支持**：使用线程池执行同步HTTP请求
- ✅ **完整错误处理**：网络错误、API错误、解析错误
- ✅ **参数配置**：采样率、格式、标点预测等
- ✅ **SSL支持**：安全的HTTPS连接

### 2. `src/connectors/asr_connector.py` (已更新)
ASR连接器集成：
- ✅ **智能服务选择**：根据配置自动选择阿里云ASR或模拟服务
- ✅ **无缝回退**：阿里云ASR失败时自动回退到模拟服务
- ✅ **统一接口**：保持原有API接口不变
- ✅ **详细日志**：记录服务选择和执行过程

### 3. `src/config.py` (已更新)
配置管理：
```python
# 阿里云ASR配置
ali_asr_app_key: str = Field(default="", description="阿里云ASR应用Key")
ali_asr_token: str = Field(default="", description="阿里云ASR服务鉴权Token")
ali_asr_enabled: bool = Field(default=False, description="是否启用阿里云ASR服务")
```

## ⚙️ 配置方法

### 方法1：环境变量配置
在 `.env` 文件中添加：
```bash
# 阿里云ASR配置
ALI_ASR_APP_KEY=your_ali_asr_app_key_here
ALI_ASR_TOKEN=your_ali_asr_token_here
ALI_ASR_ENABLED=true
```

### 方法2：直接修改配置文件
在 `src/config.py` 中修改默认值：
```python
ali_asr_app_key: str = Field(default="your_app_key", description="阿里云ASR应用Key")
ali_asr_token: str = Field(default="your_token", description="阿里云ASR服务鉴权Token")
ali_asr_enabled: bool = Field(default=True, description="是否启用阿里云ASR服务")
```

## 🔑 获取阿里云ASR凭证

1. **登录阿里云控制台**
   - 访问：https://ecs.console.aliyun.com/

2. **开通智能语音交互服务**
   - 产品与服务 → 智能语音交互
   - 开通服务并创建项目

3. **获取AppKey**
   - 在项目管理中查看AppKey

4. **获取Token**
   - 使用AccessKey和AccessSecret调用获取Token接口
   - 或在控制台的调试工具中获取临时Token

## 🎵 支持的音频格式

### 输入格式
- **编码格式**：PCM
- **采样率**：16000Hz (推荐), 8000Hz
- **位深**：16-bit
- **声道**：单声道 (推荐), 双声道
- **数据格式**：Base64编码的音频数据

### 输入方式
1. **Base64字符串**：`recognize_from_base64(base64_data)`
2. **音频文件路径**：`recognize_from_file(file_path)`
3. **直接音频数据**：`recognize_from_data(audio_bytes)`

## 🚀 使用示例

### 基本使用（通过ASR连接器）
```python
from src.connectors import ASRConnector

# 创建ASR连接器
asr = ASRConnector()

# 语音转文本（自动选择服务）
result = await asr.speech_to_text(
    audio_data="base64_encoded_audio_data",
    audio_format={
        'sampleRate': 16000,
        'channels': 1,
        'encoding': 'PCM_16BIT'
    },
    request_id="test-001"
)

print(f"识别结果: {result}")
```

### 直接使用阿里云ASR服务
```python
from src.connectors.asr_ali_remote_service import AliASRRemoteService

# 创建阿里云ASR服务
ali_asr = AliASRRemoteService()

# 从文件识别
result = await ali_asr.recognize_from_file("audio.wav")

# 从Base64数据识别
result = await ali_asr.recognize_from_base64(base64_data)

# 从音频数据识别
result = await ali_asr.recognize_from_data(audio_bytes)

print(f"识别结果: {result}")
```

## 🧪 测试方法

### 运行测试脚本
```bash
python test_ali_asr.py
```

测试内容：
- ✅ 配置检查
- ✅ 音频文件识别测试
- ✅ Base64数据识别测试
- ✅ ASR连接器集成测试
- ✅ 不同配置场景测试

### 测试输出示例
```
🧪 Ali ASR Service Tests
============================================================

🔬 Running Ali ASR Service Test...
📋 Configuration Check:
   app_key_configured: True
   token_configured: True
   host: nls-gateway-cn-shanghai.aliyuncs.com
   ...

✅ Ali ASR Service test PASSED
```

## 🔄 服务选择逻辑

系统按以下优先级选择ASR服务：

1. **阿里云ASR服务** (优先)
   - 条件：`ALI_ASR_ENABLED=true` 且 AppKey、Token已配置
   - 优势：真实语音识别，支持中文、英文等多种语言

2. **模拟ASR服务** (回退)
   - 条件：阿里云ASR不可用时自动启用
   - 优势：稳定可靠，支持多种测试场景

## 📊 服务状态监控

### 日志监控
系统会记录详细的服务选择和执行日志：
```
INFO - Using Ali ASR service for recognition
INFO - Ali ASR recognition successful, result_text=你好世界
INFO - ASR processing completed, service_type=ali_asr
```

### 健康检查
```python
# 检查ASR服务健康状态
health = await asr_connector.health_check()
service_name = asr_connector.get_service_name()

print(f"服务状态: {health}")
print(f"当前服务: {service_name}")
```

## ⚠️ 注意事项

### 1. 网络要求
- 需要访问阿里云服务器：`nls-gateway-cn-shanghai.aliyuncs.com`
- 确保防火墙允许HTTPS出站连接

### 2. 音频质量
- 推荐使用16kHz采样率的PCM格式
- 音频质量直接影响识别准确率
- 避免背景噪音过大

### 3. Token管理
- Token有有效期限制，需要定期更新
- 建议使用AccessKey动态获取Token

### 4. 错误处理
- 系统会自动处理网络错误和API错误
- 失败时自动回退到模拟服务，保证系统稳定性

## 🔧 故障排除

### 问题1：配置不生效
**解决方案**：
1. 检查环境变量是否正确设置
2. 重启应用程序
3. 检查配置文件语法

### 问题2：识别失败
**解决方案**：
1. 检查网络连接
2. 验证AppKey和Token是否正确
3. 检查音频格式是否符合要求
4. 查看详细错误日志

### 问题3：识别结果为空
**解决方案**：
1. 检查音频是否包含有效语音
2. 尝试调整音频质量
3. 检查音频长度是否合适（建议1-60秒）

## 📈 性能优化

### 1. 并发处理
- 阿里云ASR服务支持并发请求
- 系统使用异步处理，提高吞吐量

### 2. 错误重试
- 网络错误自动重试
- 失败后智能回退到模拟服务

### 3. 资源管理
- 自动管理HTTP连接
- 及时释放资源，避免内存泄漏

## 🎯 后续优化

1. **Token自动刷新**：实现Token过期自动更新
2. **缓存机制**：对相同音频内容进行缓存
3. **负载均衡**：支持多个阿里云区域
4. **实时识别**：支持流式语音识别
5. **多语言支持**：配置不同语言模型

---

**集成完成！** 🎉 现在系统支持真实的阿里云ASR服务，同时保持向后兼容和稳定性。
