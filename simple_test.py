print("Hello World")
print("Testing basic Python execution")

try:
    import sys
    print(f"Python version: {sys.version}")
    
    import os
    print(f"Current directory: {os.getcwd()}")
    
    # 测试基本导入
    import json
    print("JSON module imported successfully")
    
    import asyncio
    print("Asyncio module imported successfully")
    
    print("✅ Basic test completed successfully!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
