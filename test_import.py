#!/usr/bin/env python3
"""
测试导入是否正常
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing imports...")
    
    from src.config import settings
    print(f"✅ Config loaded: {settings.app_name}")
    
    from src.models import VoiceInteractRequest
    print("✅ Models imported successfully")
    
    from src.controllers.voice_controller import router
    print("✅ Controller imported successfully")
    
    from src.services.voice_interaction_service import VoiceInteractionService
    print("✅ Service imported successfully")
    
    print("🎉 All imports successful!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Error: {e}")
    sys.exit(1)
