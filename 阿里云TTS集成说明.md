# 阿里云TTS集成说明

## 概述

本项目已成功集成阿里云TTS（Text-to-Speech）语音合成服务，支持将文本转换为高质量的语音文件。

## 功能特性

### ✅ 核心功能
- **文本转语音**：支持中文、英文等多语言文本合成
- **多种音频格式**：支持WAV、MP3等格式输出
- **多种发音人**：支持xiaoyun等多种发音人选择
- **参数可调**：支持音量、语速、语调等参数调节
- **异步支持**：完全异步实现，支持高并发
- **错误处理**：完善的错误处理和异常恢复机制

### ✅ 技术特性
- **双请求方式**：支持POST和GET两种请求方式
- **智能服务选择**：优先使用阿里云TTS，失败时抛出异常
- **文件存储**：音频文件存储到本地tts_files目录
- **HTTP访问**：生成HTTP URL供客户端访问音频文件
- **配置灵活**：支持环境变量和配置文件配置

## 文件结构

```
src/connectors/
├── tts_ali_remote_service.py    # 阿里云TTS远程服务实现
├── tts_connector.py             # TTS连接器（已集成阿里云TTS）
└── base_connector.py            # 基础连接器

tts_files/                       # TTS音频文件存储目录
├── tts_12345_1703123456789.wav  # 生成的音频文件
└── tts_67890_1703123456790.mp3

test_ali_tts.py                  # 阿里云TTS集成测试
阿里云TTS集成说明.md              # 本文档
```

## 配置方法

### 1. 环境变量配置（推荐）

在 `.env` 文件中添加：

```bash
# 阿里云TTS配置
ALI_TTS_APP_KEY=your_ali_tts_app_key_here
ALI_TTS_TOKEN=your_ali_tts_token_here
ALI_TTS_ENABLED=true
TTS_HTTP_SERVER_HOST=************
```

### 2. 配置文件说明

- `ALI_TTS_APP_KEY`: 阿里云TTS应用Key
- `ALI_TTS_TOKEN`: 阿里云TTS服务鉴权Token
- `ALI_TTS_ENABLED`: 是否启用阿里云TTS服务
- `TTS_HTTP_SERVER_HOST`: TTS音频文件HTTP服务器地址

### 3. 获取阿里云TTS凭证

1. 登录[阿里云控制台](https://ecs.console.aliyun.com/)
2. 开通智能语音交互服务
3. 创建项目获取AppKey
4. 生成访问Token

## 使用方法

### 1. 直接使用阿里云TTS服务

```python
from src.connectors.tts_ali_remote_service import AliTTSRemoteService

# 创建服务实例
ali_tts = AliTTSRemoteService()

# 文本转语音
result = await ali_tts.text_to_speech(
    text="你好，欢迎使用阿里云TTS服务",
    params={
        'format': 'wav',
        'sample_rate': 16000,
        'voice': 'xiaoyun',
        'volume': 50,
        'speech_rate': 0,
        'pitch_rate': 0
    },
    use_post=True
)

if result['success']:
    print(f"音频文件: {result['audio_file']}")
    print(f"访问URL: {result['audio_url']}")
```

### 2. 使用TTS连接器（推荐）

```python
from src.connectors.tts_connector import TTSConnector

# 创建连接器
tts = TTSConnector()

# 文本转语音（返回URL）
audio_url = await tts.text_to_speech(
    text="智能语音交互系统已启动",
    voice_config={'voice': 'xiaoyun', 'volume': 60},
    output_format='wav',
    return_base64=False,
    request_id='demo-001'
)

# 文本转语音（返回Base64）
audio_base64 = await tts.text_to_speech(
    text="语音合成完成",
    voice_config={'voice': 'xiaoyun'},
    output_format='mp3',
    return_base64=True,
    request_id='demo-002'
)
```

## 支持的参数

### 音频格式
- `wav`: WAV格式（推荐用于高质量音频）
- `mp3`: MP3格式（推荐用于网络传输）

### 采样率
- `16000`: 16kHz（推荐，平衡质量和文件大小）
- `8000`: 8kHz（较小文件）
- `24000`: 24kHz（高质量）

### 发音人
- `xiaoyun`: 小云（女声，标准普通话）
- `xiaogang`: 小刚（男声）
- `xiaomeng`: 小梦（女声，甜美）
- 更多发音人请参考阿里云文档

### 其他参数
- `volume`: 音量（0-100，默认50）
- `speech_rate`: 语速（-500~500，默认0）
- `pitch_rate`: 语调（-500~500，默认0）

## 服务选择逻辑

系统会根据配置自动选择合适的TTS服务：

1. **阿里云TTS优先**：
   - 当 `ALI_TTS_ENABLED=true` 且配置了有效凭证时
   - 使用阿里云TTS进行真实的语音合成

2. **模拟TTS备用**：
   - 当阿里云TTS未启用或未配置时
   - 使用模拟TTS服务（返回模拟音频URL）

3. **异常处理**：
   - 阿里云TTS失败时抛出异常
   - 由外层服务进行异常处理和降级

## 测试方法

### 1. 运行集成测试

```bash
python test_ali_tts.py
```

### 2. 测试覆盖范围

- ✅ 阿里云TTS远程服务测试
- ✅ TTS连接器集成测试
- ✅ 错误处理测试
- ✅ 配置场景测试

### 3. 预期测试结果

```
🧪 Ali TTS Integration Tests
==================================================

🔬 Running Ali TTS Remote Service Test...
📋 Service configured: True/False
✅ Ali TTS Remote Service test PASSED

🔬 Running TTS Connector Integration Test...
📋 Service name: TTS Service (Ali Cloud/Mock)
✅ TTS Connector Integration test PASSED

🔬 Running TTS Error Handling Test...
✅ TTS Error Handling test PASSED

🔬 Running Configuration Scenarios Test...
✅ Configuration Scenarios test PASSED

🎯 Overall Result: 4/4 tests passed
🎉 All Ali TTS integration tests PASSED!
```

## 故障排除

### 1. 常见问题

**问题**: TTS服务返回"Token无效"错误
**解决**: 检查ALI_TTS_TOKEN是否正确，Token是否过期

**问题**: 音频文件无法访问
**解决**: 检查TTS_HTTP_SERVER_HOST配置，确保HTTP服务器正常运行

**问题**: 合成速度较慢
**解决**: 检查网络连接，考虑使用POST请求方式

### 2. 调试方法

启用详细日志：
```bash
LOG_LEVEL=DEBUG
LOG_FORMAT=text
```

查看详细的请求和响应信息。

### 3. 性能优化

- 使用POST请求方式（推荐）
- 合理设置音频格式和采样率
- 启用HTTP服务器缓存
- 考虑音频文件清理策略

## 生产部署建议

### 1. 安全配置
- 使用环境变量存储敏感凭证
- 定期轮换Token
- 限制HTTP服务器访问权限

### 2. 性能优化
- 配置音频文件清理任务
- 使用CDN加速音频文件访问
- 监控TTS服务调用量和成功率

### 3. 监控告警
- 监控TTS服务可用性
- 设置Token过期告警
- 监控音频文件存储空间

## 更新日志

- **2025-09-17**: 完成阿里云TTS集成
  - 实现AliTTSRemoteService远程服务
  - 集成到TTSConnector连接器
  - 添加完整的测试覆盖
  - 支持POST和GET两种请求方式
  - 实现异常处理和错误恢复机制
