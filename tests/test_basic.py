"""
基础测试
验证项目基础功能
"""
import pytest
from fastapi.testclient import TestClient
from src.main import app
from src.config import settings


@pytest.fixture
def client():
    """测试客户端"""
    return TestClient(app)


def test_health_check(client):
    """测试健康检查接口"""
    response = client.get("/health")
    assert response.status_code == 200
    
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == settings.app_name
    assert data["version"] == settings.app_version
    assert "timestamp" in data


def test_voice_interact_endpoint_exists(client):
    """测试语音交互接口是否存在"""
    # 发送一个不完整的请求，应该返回422验证错误而不是404
    response = client.post("/api/v1/voice/interact", json={})
    assert response.status_code == 422  # 验证错误，说明接口存在


def test_config_loading():
    """测试配置加载"""
    assert settings.app_name is not None
    assert settings.app_version is not None
    assert settings.host is not None
    assert settings.port > 0
