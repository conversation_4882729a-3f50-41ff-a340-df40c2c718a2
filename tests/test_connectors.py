"""
连接器测试
验证所有连接器的基本功能
"""
import pytest
import asyncio
from src.connectors import (
    ASRConnector, 
    LLMConnector, 
    TTSConnector, 
    PlatformServiceConnector,
    ConnectorError
)
from src.models import ExtendParams


class TestASRConnector:
    """ASR连接器测试"""
    
    @pytest.fixture
    async def asr_connector(self):
        connector = ASRConnector()
        yield connector
        await connector.close()
    
    @pytest.mark.asyncio
    async def test_speech_to_text(self, asr_connector):
        """测试语音转文本"""
        # 模拟Base64音频数据
        audio_data = "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQ=="
        
        result = await asr_connector.speech_to_text(audio_data, request_id="test-001")
        
        assert isinstance(result, str)
        assert len(result) > 0
        print(f"ASR Result: {result}")
    
    @pytest.mark.asyncio
    async def test_health_check(self, asr_connector):
        """测试健康检查"""
        result = await asr_connector.health_check()
        assert result is True
    
    def test_service_name(self, asr_connector):
        """测试服务名称"""
        name = asr_connector.get_service_name()
        assert "ASR" in name


class TestTTSConnector:
    """TTS连接器测试"""
    
    @pytest.fixture
    async def tts_connector(self):
        connector = TTSConnector()
        yield connector
        await connector.close()
    
    @pytest.mark.asyncio
    async def test_text_to_speech_url(self, tts_connector):
        """测试文本转语音（URL模式）"""
        text = "你好，这是一个测试"
        
        result = await tts_connector.text_to_speech(
            text=text,
            voice_config="default",
            return_base64=False,
            request_id="test-002"
        )
        
        assert isinstance(result, str)
        assert result.startswith("https://")
        print(f"TTS URL: {result}")
    
    @pytest.mark.asyncio
    async def test_text_to_speech_base64(self, tts_connector):
        """测试文本转语音（Base64模式）"""
        text = "你好，这是一个测试"
        
        result = await tts_connector.text_to_speech(
            text=text,
            voice_config="friendly",
            return_base64=True,
            request_id="test-003"
        )
        
        assert isinstance(result, str)
        assert len(result) > 0
        print(f"TTS Base64 length: {len(result)}")
    
    @pytest.mark.asyncio
    async def test_get_voice_list(self, tts_connector):
        """测试获取语音列表"""
        result = await tts_connector.get_voice_list()
        
        assert "voices" in result
        assert "supported_formats" in result
        print(f"Voice configs: {list(result['voices'].keys())}")


class TestPlatformServiceConnector:
    """平台服务连接器测试"""
    
    @pytest.fixture
    async def platform_connector(self):
        connector = PlatformServiceConnector()
        yield connector
        await connector.close()
    
    @pytest.fixture
    def extend_params(self):
        return ExtendParams(
            authorization="Bearer test-token",
            buildingId="1942499471542292482"
        )
    
    @pytest.mark.asyncio
    async def test_get_ai_context(self, platform_connector, extend_params):
        """测试获取AI上下文"""
        result = await platform_connector.get_ai_context(
            extend_params=extend_params,
            request_id="test-004"
        )
        
        assert "devices" in result
        assert "spaces" in result
        assert len(result["devices"]) > 0
        print(f"Devices count: {len(result['devices'])}")
        print(f"Spaces count: {len(result['spaces'])}")
    
    @pytest.mark.asyncio
    async def test_control_device(self, platform_connector, extend_params):
        """测试设备控制"""
        result = await platform_connector.control_device(
            device_id="light-01",
            action="turnOn",
            extend_params=extend_params,
            request_id="test-005"
        )
        
        assert result["success"] is True
        assert result["deviceId"] == "light-01"
        assert result["action"] == "turnOn"
        print(f"Control result: {result['message']}")
    
    @pytest.mark.asyncio
    async def test_query_device_status(self, platform_connector, extend_params):
        """测试查询设备状态"""
        result = await platform_connector.query_device_status(
            device_id="light-01",
            extend_params=extend_params,
            request_id="test-006"
        )
        
        assert result["success"] is True
        assert len(result["devices"]) == 1
        print(f"Device status: {result['devices'][0]['status']}")
    
    @pytest.mark.asyncio
    async def test_execute_tool_calls(self, platform_connector, extend_params):
        """测试执行工具调用"""
        tool_calls = [
            {
                "id": "call_001",
                "function": {
                    "name": "controlDevice",
                    "arguments": {
                        "deviceId": "light-01",
                        "action": "turnOn"
                    }
                }
            }
        ]
        
        results = await platform_connector.execute_tool_calls(
            tool_calls=tool_calls,
            extend_params=extend_params,
            request_id="test-007"
        )
        
        assert len(results) == 1
        assert results[0]["success"] is True
        print(f"Tool call result: {results[0]}")


# 注意：LLMConnector需要真实的API配置，这里只做基础测试
class TestLLMConnector:
    """LLM连接器测试"""
    
    def test_service_name(self):
        """测试服务名称"""
        connector = LLMConnector()
        name = connector.get_service_name()
        assert "LLM" in name
    
    def test_tools_definition(self):
        """测试工具定义"""
        connector = LLMConnector()
        tools = connector.tools
        
        assert len(tools) > 0
        assert any(tool["function"]["name"] == "controlDevice" for tool in tools)
        assert any(tool["function"]["name"] == "queryDeviceStatus" for tool in tools)
        print(f"Tools count: {len(tools)}")


if __name__ == "__main__":
    # 运行基础测试
    async def run_basic_tests():
        print("🧪 Running basic connector tests...")
        
        # 测试ASR连接器
        print("\n📢 Testing ASR Connector...")
        asr = ASRConnector()
        audio_data = "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQ=="
        result = await asr.speech_to_text(audio_data)
        print(f"✅ ASR Result: {result}")
        await asr.close()
        
        # 测试TTS连接器
        print("\n🔊 Testing TTS Connector...")
        tts = TTSConnector()
        result = await tts.text_to_speech("你好，这是测试")
        print(f"✅ TTS Result: {result}")
        await tts.close()
        
        # 测试平台服务连接器
        print("\n🏢 Testing Platform Service Connector...")
        platform = PlatformServiceConnector()
        extend_params = ExtendParams(
            authorization="Bearer test-token",
            buildingId="test-building"
        )
        result = await platform.get_ai_context(extend_params)
        print(f"✅ Platform Result: {len(result['devices'])} devices, {len(result['spaces'])} spaces")
        await platform.close()
        
        print("\n🎉 All basic tests passed!")
    
    asyncio.run(run_basic_tests())
