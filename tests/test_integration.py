"""
集成测试 - 端到端语音交互流程测试
"""
import pytest
import asyncio
from src.services import VoiceInteractionService
from src.models import (
    VoiceInteractRequest,
    AudioFormat,
    ExtendParams,
    InteractionType
)


class TestVoiceInteractionIntegration:
    """语音交互集成测试"""
    
    @pytest.fixture
    async def voice_service(self):
        """创建语音交互服务实例"""
        service = VoiceInteractionService()
        yield service
        await service.close()
    
    @pytest.fixture
    def sample_request(self):
        """创建示例请求"""
        return VoiceInteractRequest(
            requestId="test-integration-001",
            token="UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQ==",
            timestamp=1703123456789,
            audioData="UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT",
            audioFormat=AudioFormat(
                sampleRate=16000,
                channels=1,
                encoding="PCM_16BIT"
            ),
            extend=ExtendParams(
                authorization="Bearer test-token-integration",
                buildingId="1942499471542292482"
            )
        )
    
    @pytest.mark.asyncio
    async def test_complete_voice_interaction_flow(self, voice_service, sample_request):
        """测试完整的语音交互流程"""
        
        # 执行语音交互
        response = await voice_service.process_voice_interaction(sample_request)
        
        # 验证响应结构
        assert response.requestId == sample_request.requestId
        assert response.success is True
        assert response.code == "0"
        assert response.msg == "处理成功"
        assert response.cost > 0
        
        # 验证响应数据
        assert response.data is not None
        assert response.data.text is not None
        assert response.data.responseText is not None
        assert response.data.interactionType in [
            InteractionType.RESULT, 
            InteractionType.CONVERSATION,
            InteractionType.ERROR
        ]
        
        # 验证UI组件
        assert response.data.ui is not None
        assert "type" in response.data.ui
        assert "content" in response.data.ui
        
        print(f"✅ Integration test passed:")
        print(f"   Request ID: {response.requestId}")
        print(f"   User Text: {response.data.text}")
        print(f"   Response: {response.data.responseText}")
        print(f"   Interaction Type: {response.data.interactionType}")
        print(f"   Processing Time: {response.cost}ms")
    
    @pytest.mark.asyncio
    async def test_device_control_scenario(self, voice_service):
        """测试设备控制场景"""
        
        # 创建设备控制请求（使用特定的音频数据长度触发设备控制场景）
        request = VoiceInteractRequest(
            requestId="test-device-control-001",
            token="device-control-token",
            timestamp=1703123456789,
            audioData="A" * 200,  # 长度200，触发设备控制场景
            audioFormat=AudioFormat(
                sampleRate=16000,
                channels=1,
                encoding="PCM_16BIT"
            ),
            extend=ExtendParams(
                authorization="Bearer device-control-token",
                buildingId="1942499471542292482"
            )
        )
        
        response = await voice_service.process_voice_interaction(request)
        
        # 验证设备控制响应
        assert response.success is True
        assert response.data.interactionType in [InteractionType.RESULT, InteractionType.ERROR]
        
        # 如果有设备更新，验证结构
        if response.data.deviceUpdates:
            for update in response.data.deviceUpdates:
                assert update.deviceId is not None
                assert update.timestamp > 0
        
        print(f"✅ Device control test passed:")
        print(f"   Response: {response.data.responseText}")
        print(f"   Has device updates: {bool(response.data.deviceUpdates)}")
    
    @pytest.mark.asyncio
    async def test_conversation_scenario(self, voice_service):
        """测试普通对话场景"""
        
        # 创建对话请求（使用特定的音频数据长度触发对话场景）
        request = VoiceInteractRequest(
            requestId="test-conversation-001",
            token="conversation-token",
            timestamp=1703123456789,
            audioData="A" * 50,  # 长度50，触发对话场景
            audioFormat=AudioFormat(
                sampleRate=16000,
                channels=1,
                encoding="PCM_16BIT"
            ),
            extend=ExtendParams(
                authorization="Bearer conversation-token",
                buildingId="1942499471542292482"
            )
        )
        
        response = await voice_service.process_voice_interaction(request)
        
        # 验证对话响应
        assert response.success is True
        assert response.data.interactionType == InteractionType.CONVERSATION
        assert response.data.responseText is not None
        
        print(f"✅ Conversation test passed:")
        print(f"   Response: {response.data.responseText}")
    
    @pytest.mark.asyncio
    async def test_error_handling(self, voice_service):
        """测试错误处理"""
        
        # 创建可能导致错误的请求
        request = VoiceInteractRequest(
            requestId="test-error-001",
            token="error-token",
            timestamp=1703123456789,
            audioData="",  # 空音频数据
            audioFormat=AudioFormat(
                sampleRate=16000,
                channels=1,
                encoding="PCM_16BIT"
            ),
            extend=ExtendParams(
                authorization="Bearer error-token",
                buildingId="invalid-building-id"
            )
        )
        
        response = await voice_service.process_voice_interaction(request)
        
        # 即使有错误，服务也应该返回有效响应
        assert response.requestId == request.requestId
        assert response.data is not None
        assert response.data.responseText is not None
        
        print(f"✅ Error handling test passed:")
        print(f"   Response: {response.data.responseText}")
    
    @pytest.mark.asyncio
    async def test_multiple_concurrent_requests(self, voice_service):
        """测试并发请求处理"""
        
        # 创建多个并发请求
        requests = []
        for i in range(3):
            request = VoiceInteractRequest(
                requestId=f"test-concurrent-{i:03d}",
                token=f"concurrent-token-{i}",
                timestamp=1703123456789 + i,
                audioData="A" * (100 + i * 50),  # 不同长度的音频数据
                audioFormat=AudioFormat(
                    sampleRate=16000,
                    channels=1,
                    encoding="PCM_16BIT"
                ),
                extend=ExtendParams(
                    authorization=f"Bearer concurrent-token-{i}",
                    buildingId="1942499471542292482"
                )
            )
            requests.append(request)
        
        # 并发执行请求
        tasks = [
            voice_service.process_voice_interaction(req) 
            for req in requests
        ]
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 验证所有响应
        successful_responses = 0
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                print(f"❌ Request {i} failed: {response}")
            else:
                assert response.requestId == requests[i].requestId
                assert response.success is True
                successful_responses += 1
        
        assert successful_responses > 0  # 至少有一个成功
        
        print(f"✅ Concurrent requests test passed:")
        print(f"   Successful responses: {successful_responses}/{len(requests)}")


if __name__ == "__main__":
    # 运行基础集成测试
    async def run_integration_tests():
        print("🧪 Running integration tests...")
        
        service = VoiceInteractionService()
        
        try:
            # 测试基本流程
            request = VoiceInteractRequest(
                requestId="manual-test-001",
                token="manual-test-token",
                timestamp=1703123456789,
                audioData="UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQ==",
                audioFormat=AudioFormat(
                    sampleRate=16000,
                    channels=1,
                    encoding="PCM_16BIT"
                ),
                extend=ExtendParams(
                    authorization="Bearer manual-test-token",
                    buildingId="1942499471542292482"
                )
            )
            
            response = await service.process_voice_interaction(request)
            
            print(f"✅ Manual integration test passed:")
            print(f"   Request ID: {response.requestId}")
            print(f"   Success: {response.success}")
            print(f"   User Text: {response.data.text}")
            print(f"   Response: {response.data.responseText}")
            print(f"   Processing Time: {response.cost}ms")
            
        finally:
            await service.close()
    
    asyncio.run(run_integration_tests())
