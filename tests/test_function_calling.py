"""
Function Calling功能测试
测试工具管理器、工具执行和LLM集成
"""
import pytest
import asyncio
from src.tools import ToolManager, DeviceControlTool, DeviceQueryTool
from src.tools.base_tool import ToolExecutionStatus
from src.connectors import PlatformServiceConnector


class TestToolManager:
    """工具管理器测试"""
    
    @pytest.fixture
    async def tool_manager(self):
        """创建工具管理器实例"""
        manager = ToolManager()
        yield manager
        await manager.close()
    
    @pytest.mark.asyncio
    async def test_tool_registration(self, tool_manager):
        """测试工具注册"""
        # 检查默认工具是否已注册
        available_tools = tool_manager.get_available_tools()
        assert "controlDevice" in available_tools
        assert "queryDeviceStatus" in available_tools
        
        # 检查工具定义
        definitions = tool_manager.get_tool_definitions()
        assert len(definitions) >= 2
        
        # 验证工具定义格式
        for definition in definitions:
            assert "type" in definition
            assert definition["type"] == "function"
            assert "function" in definition
            assert "name" in definition["function"]
            assert "description" in definition["function"]
            assert "parameters" in definition["function"]
        
        print(f"✅ Tool registration test passed:")
        print(f"   Available tools: {available_tools}")
        print(f"   Tool definitions count: {len(definitions)}")
    
    @pytest.mark.asyncio
    async def test_device_control_tool(self, tool_manager):
        """测试设备控制工具"""
        # 准备测试参数
        call_id = "test-control-001"
        parameters = {
            "deviceId": "light-01",
            "deviceName": "会议室灯光",
            "action": "turnOn"
        }
        context = {
            "authorization": "Bearer test-token",
            "buildingId": "test-building",
            "permissions": {"canControl": True, "canQuery": True}
        }
        
        # 执行工具调用
        result = await tool_manager.execute_tool_call(
            tool_name="controlDevice",
            call_id=call_id,
            parameters=parameters,
            context=context
        )
        
        # 验证结果
        assert result.tool_name == "controlDevice"
        assert result.call_id == call_id
        assert result.success is True
        assert result.status == ToolExecutionStatus.SUCCESS
        assert result.execution_time_ms is not None
        assert result.execution_time_ms > 0
        
        print(f"✅ Device control tool test passed:")
        print(f"   Tool: {result.tool_name}")
        print(f"   Status: {result.status.value}")
        print(f"   Message: {result.message}")
        print(f"   Execution time: {result.execution_time_ms}ms")
    
    @pytest.mark.asyncio
    async def test_device_query_tool(self, tool_manager):
        """测试设备查询工具"""
        # 准备测试参数
        call_id = "test-query-001"
        parameters = {
            "deviceId": "light-01",
            "deviceName": "会议室灯光"
        }
        context = {
            "authorization": "Bearer test-token",
            "buildingId": "test-building",
            "permissions": {"canControl": True, "canQuery": True}
        }
        
        # 执行工具调用
        result = await tool_manager.execute_tool_call(
            tool_name="queryDeviceStatus",
            call_id=call_id,
            parameters=parameters,
            context=context
        )
        
        # 验证结果
        assert result.tool_name == "queryDeviceStatus"
        assert result.call_id == call_id
        assert result.success is True
        assert result.status == ToolExecutionStatus.SUCCESS
        
        print(f"✅ Device query tool test passed:")
        print(f"   Tool: {result.tool_name}")
        print(f"   Status: {result.status.value}")
        print(f"   Message: {result.message}")
    
    @pytest.mark.asyncio
    async def test_batch_tool_calls(self, tool_manager):
        """测试批量工具调用"""
        # 准备批量工具调用
        tool_calls = [
            {
                "id": "call_001",
                "function": {
                    "name": "controlDevice",
                    "arguments": {
                        "deviceId": "light-01",
                        "action": "turnOn",
                        "deviceName": "会议室灯光"
                    }
                }
            },
            {
                "id": "call_002", 
                "function": {
                    "name": "controlDevice",
                    "arguments": {
                        "deviceId": "ac-01",
                        "action": "setTemperature",
                        "value": 24,
                        "deviceName": "会议室空调"
                    }
                }
            },
            {
                "id": "call_003",
                "function": {
                    "name": "queryDeviceStatus",
                    "arguments": {
                        "deviceId": "light-01",
                        "deviceName": "会议室灯光"
                    }
                }
            }
        ]
        
        context = {
            "authorization": "Bearer test-token",
            "buildingId": "test-building",
            "permissions": {"canControl": True, "canQuery": True}
        }
        
        # 执行批量工具调用
        results = await tool_manager.execute_tool_calls(
            tool_calls=tool_calls,
            context=context,
            max_concurrent=2
        )
        
        # 验证结果
        assert len(results) == 3
        
        successful_count = sum(1 for r in results if r.success)
        assert successful_count > 0  # 至少有一个成功
        
        print(f"✅ Batch tool calls test passed:")
        print(f"   Total calls: {len(results)}")
        print(f"   Successful calls: {successful_count}")
        
        for i, result in enumerate(results):
            print(f"   Call {i+1}: {result.tool_name} - {result.status.value}")
    
    @pytest.mark.asyncio
    async def test_parameter_validation(self, tool_manager):
        """测试参数验证"""
        # 测试缺少必需参数
        call_id = "test-validation-001"
        invalid_parameters = {
            "deviceName": "测试设备"
            # 缺少 deviceId 和 action
        }
        context = {
            "authorization": "Bearer test-token",
            "buildingId": "test-building",
            "permissions": {"canControl": True, "canQuery": True}
        }
        
        result = await tool_manager.execute_tool_call(
            tool_name="controlDevice",
            call_id=call_id,
            parameters=invalid_parameters,
            context=context
        )
        
        # 验证参数验证失败
        assert result.success is False
        assert result.status == ToolExecutionStatus.INVALID_PARAMS
        assert "Missing required parameter" in result.message
        
        print(f"✅ Parameter validation test passed:")
        print(f"   Status: {result.status.value}")
        print(f"   Error message: {result.message}")
    
    @pytest.mark.asyncio
    async def test_permission_check(self, tool_manager):
        """测试权限检查"""
        call_id = "test-permission-001"
        parameters = {
            "deviceId": "light-01",
            "action": "turnOn"
        }
        # 没有控制权限的上下文
        context = {
            "authorization": "Bearer test-token",
            "buildingId": "test-building",
            "permissions": {"canControl": False, "canQuery": True}
        }
        
        result = await tool_manager.execute_tool_call(
            tool_name="controlDevice",
            call_id=call_id,
            parameters=parameters,
            context=context
        )
        
        # 验证权限检查失败
        assert result.success is False
        assert result.status == ToolExecutionStatus.PERMISSION_DENIED
        assert "Insufficient permissions" in result.message
        
        print(f"✅ Permission check test passed:")
        print(f"   Status: {result.status.value}")
        print(f"   Error message: {result.message}")
    
    @pytest.mark.asyncio
    async def test_unknown_tool(self, tool_manager):
        """测试未知工具调用"""
        call_id = "test-unknown-001"
        parameters = {"test": "value"}
        context = {
            "authorization": "Bearer test-token",
            "buildingId": "test-building",
            "permissions": {"canControl": True, "canQuery": True}
        }
        
        result = await tool_manager.execute_tool_call(
            tool_name="unknownTool",
            call_id=call_id,
            parameters=parameters,
            context=context
        )
        
        # 验证工具未找到
        assert result.success is False
        assert result.status == ToolExecutionStatus.FAILED
        assert "not found" in result.message
        
        print(f"✅ Unknown tool test passed:")
        print(f"   Status: {result.status.value}")
        print(f"   Error message: {result.message}")


class TestDeviceTools:
    """设备工具测试"""
    
    @pytest.fixture
    async def device_control_tool(self):
        """创建设备控制工具实例"""
        tool = DeviceControlTool()
        yield tool
        # 工具本身不需要特殊清理
    
    @pytest.fixture
    async def device_query_tool(self):
        """创建设备查询工具实例"""
        tool = DeviceQueryTool()
        yield tool
        # 工具本身不需要特殊清理
    
    @pytest.mark.asyncio
    async def test_device_control_brightness(self, device_control_tool):
        """测试亮度控制"""
        call_id = "test-brightness-001"
        parameters = {
            "deviceId": "light-01",
            "deviceName": "客厅灯光",
            "action": "setBrightness",
            "value": 80
        }
        context = {
            "authorization": "Bearer test-token",
            "buildingId": "test-building",
            "permissions": {"canControl": True, "canQuery": True}
        }
        
        result = await device_control_tool._execute_with_timing(
            call_id, parameters, context
        )
        
        assert result.success is True
        assert result.data["action"] == "setBrightness"
        assert result.data["value"] == 80
        
        print(f"✅ Brightness control test passed:")
        print(f"   Device: {result.data['deviceName']}")
        print(f"   Action: {result.data['action']}")
        print(f"   Value: {result.data['value']}")
    
    @pytest.mark.asyncio
    async def test_device_control_temperature(self, device_control_tool):
        """测试温度控制"""
        call_id = "test-temperature-001"
        parameters = {
            "deviceId": "ac-01",
            "deviceName": "客厅空调",
            "action": "setTemperature",
            "value": 22
        }
        context = {
            "authorization": "Bearer test-token",
            "buildingId": "test-building",
            "permissions": {"canControl": True, "canQuery": True}
        }
        
        result = await device_control_tool._execute_with_timing(
            call_id, parameters, context
        )
        
        assert result.success is True
        assert result.data["action"] == "setTemperature"
        assert result.data["value"] == 22
        
        print(f"✅ Temperature control test passed:")
        print(f"   Device: {result.data['deviceName']}")
        print(f"   Action: {result.data['action']}")
        print(f"   Value: {result.data['value']}°C")


if __name__ == "__main__":
    # 运行基础Function Calling测试
    async def run_function_calling_tests():
        print("🧪 Running Function Calling tests...")
        
        # 测试工具管理器
        print("\n🔧 Testing Tool Manager...")
        manager = ToolManager()
        
        try:
            # 测试工具注册
            tools = manager.get_available_tools()
            print(f"✅ Available tools: {tools}")
            
            # 测试工具定义
            definitions = manager.get_tool_definitions()
            print(f"✅ Tool definitions count: {len(definitions)}")
            
            # 测试设备控制
            result = await manager.execute_tool_call(
                tool_name="controlDevice",
                call_id="manual-test-001",
                parameters={
                    "deviceId": "light-01",
                    "action": "turnOn",
                    "deviceName": "测试灯光"
                },
                context={
                    "authorization": "Bearer test-token",
                    "buildingId": "test-building",
                    "permissions": {"canControl": True, "canQuery": True}
                }
            )
            
            print(f"✅ Device control test:")
            print(f"   Success: {result.success}")
            print(f"   Message: {result.message}")
            print(f"   Execution time: {result.execution_time_ms}ms")
            
        finally:
            await manager.close()
        
        print("\n🎉 Function Calling tests completed!")
    
    asyncio.run(run_function_calling_tests())
