"""
调试Base64数据问题
"""
import base64
import re

# 测试数据
test_audio_data = "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT"

print("🔍 Base64 Data Analysis")
print("=" * 50)
print(f"Original data: {test_audio_data}")
print(f"Length: {len(test_audio_data)}")
print(f"Length % 4: {len(test_audio_data) % 4}")

# 检查字符集
print("\n📊 Character Set Analysis")
valid_chars = set("ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=")
invalid_chars = set(test_audio_data) - valid_chars
print(f"Invalid characters: {invalid_chars}")

# 检查每个字符
print("\n🔤 Character by Character Analysis")
for i, char in enumerate(test_audio_data):
    if char not in valid_chars:
        print(f"Invalid char at position {i}: '{char}' (ASCII: {ord(char)})")

# 尝试不同的正则表达式
print("\n🧪 Regex Testing")
patterns = [
    r'^[A-Za-z0-9+/]*={0,2}$',
    r'^[A-Za-z0-9+/\-_]*={0,2}$',
    r'^[A-Za-z0-9+/]*={0,3}$',
    r'^[A-Za-z0-9+/\-_]*={0,3}$'
]

for i, pattern in enumerate(patterns):
    match = re.match(pattern, test_audio_data)
    print(f"Pattern {i+1}: {pattern} -> {'✅ Match' if match else '❌ No match'}")

# 尝试添加padding
print("\n🔧 Padding Analysis")
missing_padding = len(test_audio_data) % 4
if missing_padding:
    padded_data = test_audio_data + '=' * (4 - missing_padding)
    print(f"Missing padding: {4 - missing_padding}")
    print(f"Padded data: {padded_data}")
    print(f"Padded length: {len(padded_data)}")
    
    # 测试解码
    try:
        decoded = base64.b64decode(padded_data, validate=True)
        print(f"✅ Decode successful! Size: {len(decoded)} bytes")
        
        # 检查是否是WAV文件
        if len(decoded) >= 12:
            header = decoded[:12]
            print(f"Header: {header}")
            if header[:4] == b'RIFF' and header[8:12] == b'WAVE':
                print("✅ This is a WAV file!")
            else:
                print("❌ Not a WAV file")
        
    except Exception as e:
        print(f"❌ Decode failed: {e}")
else:
    print("No padding needed")
    
    # 测试解码
    try:
        decoded = base64.b64decode(test_audio_data, validate=True)
        print(f"✅ Decode successful! Size: {len(decoded)} bytes")
        
        # 检查是否是WAV文件
        if len(decoded) >= 12:
            header = decoded[:12]
            print(f"Header: {header}")
            if header[:4] == b'RIFF' and header[8:12] == b'WAVE':
                print("✅ This is a WAV file!")
            else:
                print("❌ Not a WAV file")
        
    except Exception as e:
        print(f"❌ Decode failed: {e}")

print("\n🎯 Conclusion")
print("=" * 50)
if invalid_chars:
    print(f"❌ Found invalid characters: {invalid_chars}")
else:
    print("✅ All characters are valid Base64")
