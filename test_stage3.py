"""
阶段三功能验证测试
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services import VoiceInteractionService
from src.models import (
    VoiceInteractRequest,
    AudioFormat,
    ExtendParams
)


async def test_stage3_functionality():
    """测试阶段三的核心功能"""
    print("🧪 Testing Stage 3: Core Business Logic")
    print("=" * 50)
    
    # 创建服务实例
    service = VoiceInteractionService()
    
    try:
        # 创建测试请求
        request = VoiceInteractRequest(
            requestId="stage3-test-001",
            token="stage3-test-token",
            timestamp=1703123456789,
            audioData="UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT",
            audioFormat=AudioFormat(
                sampleRate=16000,
                channels=1,
                encoding="PCM_16BIT"
            ),
            extend=ExtendParams(
                authorization="Bearer stage3-test-token",
                buildingId="1942499471542292482"
            )
        )
        
        print("📝 Test Request:")
        print(f"   Request ID: {request.requestId}")
        print(f"   Building ID: {request.extend.building_id}")
        print(f"   Audio Data Length: {len(request.audioData)}")
        print()
        
        # 执行语音交互
        print("🚀 Processing voice interaction...")
        response = await service.process_voice_interaction(request)
        
        # 验证响应
        print("✅ Response received:")
        print(f"   Request ID: {response.requestId}")
        print(f"   Success: {response.success}")
        print(f"   Code: {response.code}")
        print(f"   Message: {response.msg}")
        print(f"   Processing Time: {response.cost}ms")
        print()
        
        print("📊 Response Data:")
        print(f"   User Text: {response.data.text}")
        print(f"   Response Text: {response.data.responseText}")
        print(f"   Interaction Type: {response.data.interactionType}")
        print(f"   Audio URL: {response.data.audioUrl}")
        print()
        
        print("🎨 UI Component:")
        if response.data.ui:
            print(f"   Type: {response.data.ui.get('type')}")
            print(f"   Title: {response.data.ui.get('title')}")
            print(f"   Status: {response.data.ui.get('status')}")
        print()
        
        print("📱 Device Updates:")
        if response.data.deviceUpdates:
            for update in response.data.deviceUpdates:
                print(f"   Device: {update.deviceId} ({update.deviceName})")
                print(f"   Properties: {update.properties}")
        else:
            print("   No device updates")
        print()
        
        print("🔄 Context Data:")
        if response.data.contextData:
            print(f"   Session ID: {response.data.contextData.sessionId}")
            print(f"   Conversation ID: {response.data.contextData.conversationId}")
            if response.data.contextData.lastOperation:
                print(f"   Last Operation: {response.data.contextData.lastOperation}")
        else:
            print("   No context data")
        print()
        
        # 验证核心功能
        assert response.requestId == request.requestId
        assert response.success is True
        assert response.code == "0"
        assert response.data is not None
        assert response.data.text is not None
        assert response.data.responseText is not None
        
        print("🎉 Stage 3 functionality test PASSED!")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理资源
        await service.close()


async def test_multiple_scenarios():
    """测试多种场景"""
    print("\n🧪 Testing Multiple Scenarios")
    print("=" * 50)
    
    scenarios = [
        {
            "name": "Device Control",
            "audio_length": 200,  # 触发设备控制场景
            "expected_type": "RESULT"
        },
        {
            "name": "Conversation", 
            "audio_length": 50,   # 触发对话场景
            "expected_type": "CONVERSATION"
        },
        {
            "name": "Complex Query",
            "audio_length": 300,  # 触发复杂查询场景
            "expected_type": "RESULT"
        }
    ]
    
    service = VoiceInteractionService()
    
    try:
        for i, scenario in enumerate(scenarios):
            print(f"\n📋 Scenario {i+1}: {scenario['name']}")
            
            request = VoiceInteractRequest(
                requestId=f"scenario-{i+1:03d}",
                token=f"scenario-token-{i+1}",
                timestamp=1703123456789 + i,
                audioData="A" * scenario["audio_length"],
                audioFormat=AudioFormat(
                    sampleRate=16000,
                    channels=1,
                    encoding="PCM_16BIT"
                ),
                extend=ExtendParams(
                    authorization=f"Bearer scenario-token-{i+1}",
                    buildingId="1942499471542292482"
                )
            )
            
            response = await service.process_voice_interaction(request)
            
            print(f"   ✅ Success: {response.success}")
            print(f"   📝 User Text: {response.data.text}")
            print(f"   💬 Response: {response.data.responseText}")
            print(f"   🎯 Type: {response.data.interactionType}")
            print(f"   ⏱️  Time: {response.cost}ms")
            
        print(f"\n🎉 All {len(scenarios)} scenarios completed!")
        
    finally:
        await service.close()


if __name__ == "__main__":
    async def main():
        # 测试基本功能
        success = await test_stage3_functionality()
        
        if success:
            # 测试多种场景
            await test_multiple_scenarios()
        
        print("\n🏁 Stage 3 testing completed!")
    
    asyncio.run(main())
