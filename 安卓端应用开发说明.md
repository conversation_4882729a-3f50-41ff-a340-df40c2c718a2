# InxVision面板应用

基于WebView的Android智能面板应用，集成了完整的语音交互功能。通过JavaScript桥接实现H5与原生功能的双向通信，支持热词唤醒、语音识别、VAD检测等AI语音能力。

## 功能特点

### 核心功能

- **WebView容器**：为H5页面提供原生容器
- **语音交互**：集成Sherpa-ONNX，支持热词唤醒和语音识别
- **双向通信**：通过JsBridge实现H5与原生的无缝通信
- **网络通信**：支持语音数据上传和服务端响应处理

### 语音功能

- **热词监听**：后台持续监听，支持多关键词
- **VAD检测**：实时语音活动检测和音频录制
- **并发处理**：支持多个语音请求并发处理
- **状态管理**：完整的语音交互状态跟踪
- **回音消除**：播放期间暂停VAD录音，支持热词打断

### 系统功能

- **WiFi管理**：WiFi信息获取和变化通知
- **配置管理**：H5地址和语音服务地址动态配置功能
- **缓存支持**：完整的WebView缓存机制
- **扩展架构**：模块化设计，易于功能扩展

## 技术详情

### 基础信息

- **最低支持版本**：Android 5.0 (API 21)
- **目标版本**：Android 14 (API 34)
- **开发语言**：Java
- **语音引擎**：Sherpa-ONNX
- **网络协议**：HTTP/HTTPS + JSON

### 技术架构

- **前端**：H5 + JavaScript
- **原生**：Android WebView + JsBridge
- **语音处理**：KWS (关键词识别) + VAD (语音活动检测)
- **网络通信**：异步HTTP请求 + 请求队列管理
- **状态管理**：并发状态管理器

## 项目结构

### 核心模块

- **语音交互模块** (`voice/`)：完整的语音交互功能
- **网络通信模块** (`http/`)：HTTP请求响应处理
- **JsBridge模块** (`bridge/`)：H5与原生通信桥接
- **工具模块** (`utils/`)：配置管理和工具类

### 主要类说明

- `VoiceInteractionManager`：语音交互核心管理器
- `KeywordListenerService`：热词监听后台服务
- `VadDetectionModule`：VAD检测模块
- `AudioStateManager`：音频播放状态管理器（回音消除）
- `NetworkClient`：HTTP网络客户端
- `JsBridge`：JavaScript桥接器
- `MainActivity`：主Activity容器

详细的代码结构说明请参考 [code_desc.md](./code_desc.md)

## JavaScript API

应用通过两个主要对象暴露JavaScript API：

- `AndroidNative`：系统功能接口
- `VoiceInteraction`：语音交互接口

### 1. 语音交互接口 (VoiceInteraction)

#### 配置和控制接口

```javascript
// 配置语音交互参数，主要extend参数，把用户登录后获取的空间信息等参数，需要传递，具体开发时再定
VoiceInteraction.configVoiceInteraction(JSON.stringify({
    vadTimeout: 15,
    serverUrl: "https://your-api.com/voice",
    extend: {
        authorization: "bearer 6dd7cc7a-f44e-45cc-ba5b-c95ef2e95dc5",
        buildingId: "1942499471542292482"
    }
}));
```

**配置参数说明：**

| 参数                    | 类型     | 必填  | 说明                      |
| --------------------- | ------ | --- | ----------------------- |
| vadTimeout            | Number | 否   | VAD超时时间（秒），默认15秒        |
| serverUrl             | String | 是   | 语音识别服务端URL（可通过长按配置界面设置） |
| extend                | Object | 是   | 扩展参数，包含用户登录和空间信息        |
| extend.authorization  | String | 是   | 用户屏端登录会话信息，即sessionId   |
| extend.buildingId     | String | 是   | 楼宇空间ID，固定值或动态获取        |

```javascript
// 启动语音交互服务
VoiceInteraction.startVoiceInteraction();

// 停止语音交互服务
VoiceInteraction.stopVoiceInteraction();

// 手动启动VAD检测
VoiceInteraction.startVadDetection();

// 停止VAD检测
VoiceInteraction.stopVadDetection();

// 获取当前语音状态
VoiceInteraction.getVoiceState();

// 获取运行时统计信息
VoiceInteraction.getRuntimeStats();

// 测试连接
VoiceInteraction.testConnection();

// 回音消除相关接口
VoiceInteraction.setSystemAudioPlaying(true);  // 设置音频播放状态
VoiceInteraction.isSystemAudioPlaying();       // 获取音频播放状态
VoiceInteraction.interruptSystemAudio();       // 中断音频播放（热词打断）
```

#### 关键词配置

关键词检测使用固定的配置文件，不支持动态配置，确保与KWS模型的一致性：

- **配置文件**：`app/src/main/assets/keywords.txt`

- **格式**：拼音 + @ + 中文，每行一个关键词

- **示例**：
  
  ```
  n ǐ h ǎo j ūn g ē @你好军哥
  x iǎo ài t óng x ué @小爱同学
  x iǎo m ǐ x iǎo m ǐ @小米小米
  ```

**注意**：修改关键词需要重新编译应用，不支持运行时动态修改。

#### 语音事件回调函数

H5页面需要定义以下回调函数来接收语音事件：

```javascript
// 热词检测回调
function handleKeywordDetected(data) {
    const { keyword, confidence, timestamp } = JSON.parse(data);
    console.log('检测到关键词:', keyword, '置信度:', confidence);
    // 处理热词检测事件，如播放"我在"提示音
}

// VAD检测开始
function handleVadStarted(data) {
    const { timestamp } = JSON.parse(data);
    console.log('VAD检测开始');
    // 显示录音状态UI
}

// 检测到语音活动
function handleSpeechDetected(data) {
    const { timestamp } = JSON.parse(data);
    console.log('检测到语音');
    // 显示正在说话状态
}

// 语音结束
function handleSpeechEnded(data) {
    const { audioLength, duration, timestamp, audioData } = JSON.parse(data);
    console.log('语音结束，时长:', duration + 'ms', '数据大小:', audioLength);
    // 显示处理中状态
}

// VAD检测停止
function handleVadStopped(data) {
    const { timestamp } = JSON.parse(data);
    console.log('VAD检测停止');
    // 恢复默认状态
}

// VAD超时
function handleVadTimeout(data) {
    const { timestamp } = JSON.parse(data);
    console.log('VAD检测超时');
    // 显示超时提示
}

// 服务端响应
function handleServerResponse(data) {
    const response = JSON.parse(data);
    if (response.success) {
        console.log('识别结果:', response.data.text);
        console.log('处理耗时:', response.cost + 'ms');

        if (response.data.audioUrl) {
            // 播放音频回复（使用AudioManager）
            AudioManager.playResponseAudio(response.data.audioUrl).then(success => {
                if (success) {
                    console.log('音频回复播放完成');
                }
            });
        }
        if (response.data.audioBase64) {
            // 播放Base64音频回复
            AudioManager.playBase64Audio(response.data.audioBase64).then(success => {
                if (success) {
                    console.log('Base64音频播放完成');
                }
            });
        }

        // 处理其他业务数据
        if (response.data.intent) {
            console.log('意图识别:', response.data.intent);
        }
        if (response.data.entities) {
            console.log('实体识别:', response.data.entities);
        }
    } else {
        console.error('识别失败:', response.code, response.msg);
        // 显示错误信息
    }
}

// 状态变更通知
function handleStateChanged(data) {
    const { stateName, newValue, timestamp } = JSON.parse(data);
    console.log('状态变更:', stateName, '=', newValue);
    // 更新UI状态
}

// 错误处理
function handleVoiceError(data) {
    const { type, message, timestamp } = JSON.parse(data);
    console.error('语音错误:', type, message);
    // 显示错误提示
}
```

#### 回音消除功能

应用集成了完整的回音消除功能，解决系统播放音频时VAD录入问题：

**核心机制**：

- 系统播放音频时，VAD自动暂停录音
- 播放结束后，VAD自动恢复录音
- 支持热词打断长音频播放

**H5端音频播放**：

```javascript
// 使用统一的AudioManager播放音频（所有方法都是异步的）
await AudioManager.playSound('prompt');                    // 播放提示音，等待播放完成
await AudioManager.playResponseAudio(audioUrl);           // 播放服务端音频，等待播放完成
await AudioManager.playBase64Audio(base64Data);           // 播放Base64音频，等待播放完成

// 或者使用Promise方式
AudioManager.playSound('prompt').then(success => {
    if (success) {
        console.log('播放成功');
    }
});

AudioManager.stopCurrentAudio();                          // 停止当前音频（热词打断）
```

**重要说明**：

- 所有播放方法现在都会等待音频播放完成才返回
- 播放期间会自动通知原生端暂停VAD录音
- 播放完成后会自动恢复VAD录音
- 支持热词打断正在播放的音频

**使用场景**：

1. **正常交互**：热词 → 播放"我在"（VAD暂停） → 播放结束（VAD恢复） → 用户说话
2. **热词打断**：播放音频期间说热词 → 立即停止播放 → VAD恢复录音
3. **长音频打断**：服务端长音频播放时热词打断，支持重新开始交互

### 2. 系统功能接口 (AndroidNative)

#### WiFi信息

```javascript
// 获取当前WiFi信息
AndroidNative.getWifiInfo("回调函数名");

// 注册WiFi变化通知
AndroidNative.registerWifiChangeCallback("回调函数名");
```

WiFi回调函数格式：

```json
{
  "ssid": "WiFi名称",
  "bssid": "MAC地址",
  "ipAddress": "IP地址"
}
```

## 服务端接口定义

### 语音识别接口

#### 请求接口

**接口地址**：由H5配置的`serverUrl`决定
**请求方法**：POST
**Content-Type**：application/json

#### 请求格式

```json
{
  //唯一请求ID
  "requestId": "550e8400-e29b-41d4-a716-446655440000",
  //授权访问服务端加密参数
  "token": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0Y"
  //当前时间戳
  "timestamp": 1703123456789,
  //音频数据
  "audioData": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT...",
  //音频格式  
  "audioFormat": {
    "sampleRate": 16000,
    "channels": 1,
    "encoding": "PCM_16BIT"
  },
  //业务扩展参数
  "extend": {
    //用户屏端登录信息
    "authorization": "bearer 6dd7cc7a-f44e-45cc-ba5b-c95ef2e95dc5"
    //楼宇空间
    "buildingId": "1942499471542292482",
  }
}
```

#### 请求字段说明

| 字段                     | 类型      | 必填  | 说明                    |
| ---------------------- | ------- | --- | --------------------- |
| requestId              | String  | 是   | 唯一请求标识，UUID格式         |
| token                  | String  | 是   | 授权访问服务端加密参数           |
| timestamp              | Long    | 是   | 请求时间戳（毫秒）             |
| audioData              | String  | 是   | Base64编码的音频数据         |
| audioFormat            | Object  | 是   | 音频格式信息                |
| audioFormat.sampleRate | Integer | 是   | 采样率，固定16000           |
| audioFormat.channels   | Integer | 是   | 声道数，固定1               |
| audioFormat.encoding   | String  | 是   | 编码格式，固定"PCM_16BIT"    |
| extend                 | Object  | 是   | 扩展参数，由H5传入，支持任意键值对    |
| extend.authorization   | String  | 是   | 用户屏端登录会话信息，即sessionId |
| extend.buildingId      | String  | 是   | 1942499471542292482   |

#### Token生成机制

`token`字段用于API访问授权，采用MD5加密算法生成：

**生成算法**：
```
token = MD5(access_key + access_secret + timestamp)
```

**参数说明**：
- `access_key`：可通过配置界面修改的授权码，默认值：`inxvision_panel_2024`
- `access_secret`：固定密钥，存储在应用内部：`9f8e7d6c5b4a3210fedcba0987654321`
- `timestamp`：与请求中的timestamp字段保持一致

**安全机制**：
- 服务端使用相同的access_key、access_secret和收到的timestamp进行验证
- 每次请求的token都不同（基于时间戳）
- access_secret不会在网络传输中暴露

#### 扩展参数说明

`extend`字段支持传入任意的键值对，用于满足不同业务场景的需求：

```json
{
  "extend": {
    //用户屏端登录信息
    "authorization": "bearer 6dd7cc7a-f44e-45cc-ba5b-c95ef2e95dc5"
    //楼宇空间
    "buildingId": "1942499471542292482",

    // 其他可扩展任何业务参数
  }
}
```

**特点：**

- 支持任意数量的键值对
- 键名由业务方自定义
- 值支持字符串、数字、布尔值等基本类型
- 服务端会原样返回这些参数，便于业务处理

#### 响应格式

##### 成功响应

```json
{
  "requestId": "550e8400-e29b-41d4-a716-446655440000",
  "success": true,
  "timestamp": 1703123456789,
  "cost": 1500,
  "data": {
    "text": "今天天气怎么样",
    "audioUrl": "https://your-cdn.com/audio/response.mp3",
    "audioBase64": "UklGRnoGAABXQVZFZm10IBAAAAABAAEAQB8AAEAfAAABAAgAZGF0YQoGAACBhYqFbF1fdJivrJBhNjVgodDbq2EcBj+a2/LDciUFLIHO8tiJNwgZaLvt559NEAxQp+PwtmMcBjiR1/LMeSwFJHfH8N2QQAoUXrTp66hVFApGn+DyvmwhBSuBzvLZiTYIG2m98OScTgwOUarm7blmGgU7k9n1unEiBC13yO/eizEIHWq+8+OWT...",
    "intent": "weather_query",
    "entities": {
      "location": "当前位置",
      "time": "今天"
    },
    "confidence": 0.95
  },
  "code": "0",
  "msg": "处理成功"
}
```

##### 错误响应

```json
{
  "requestId": "550e8400-e29b-41d4-a716-446655440000",
  "success": false,
  "timestamp": 1703123456789,
  "data": {},
  "code": "AUDIO_DECODE_ERROR",
  "msg": "音频数据解码失败"
}
```

#### 响应字段说明

##### 成功响应字段

| 字段               | 类型      | 必填  | 说明                  |
| ---------------- | ------- | --- | ------------------- |
| requestId        | String  | 是   | 对应的请求ID             |
| success          | Boolean | 是   | 固定为true             |
| timestamp        | Long    | 是   | 响应时间戳（毫秒）           |
| cost             | Long    | 否   | 服务端处理时间（毫秒）         |
| data             | Object  | 是   | 响应数据（由业务方自定义内容）     |
| data.text        | String  | 否   | 识别的文本内容             |
| data.audioUrl    | String  | 否   | 音频回复的URL地址          |
| data.audioBase64 | String  | 否   | Base64编码的音频回复数据     |
| data.*           | Any     | 否   | 其他业务自定义字段(后期交互设计再定) |
| code             | String  | 是   | 响应码，成功时为"0"         |
| msg              | String  | 是   | 响应消息                |

##### 错误响应字段

| 字段        | 类型      | 必填  | 说明        |
| --------- | ------- | --- | --------- |
| requestId | String  | 是   | 对应的请求ID   |
| success   | Boolean | 是   | 固定为false  |
| timestamp | Long    | 是   | 响应时间戳（毫秒） |
| data      | Object  | 是   | 空对象       |
| code      | String  | 是   | 错误代码      |
| msg       | String  | 是   | 错误描述      |

#### 常见错误代码

| 错误代码                | 说明       |
| ------------------- | -------- |
| AUDIO_DECODE_ERROR  | 音频数据解码失败 |
| AUDIO_FORMAT_ERROR  | 音频格式不支持  |
| AUDIO_TOO_SHORT     | 音频时长过短   |
| AUDIO_TOO_LONG      | 音频时长过长   |
| ASR_SERVICE_ERROR   | 语音识别服务异常 |
| INVALID_REQUEST     | 请求参数无效   |
| RATE_LIMIT_EXCEEDED | 请求频率超限   |
| INTERNAL_ERROR      | 服务器内部错误  |

## 演示页面

应用包含一个位于`app/src/main/assets/voice.html`的演示页面，用于测试语音交互功能。这个页面提供：

### 功能特性
- **语音交互测试**：完整的语音识别流程演示
- **配置管理**：支持动态配置语音服务参数
- **状态监控**：实时显示语音交互状态
- **日志查看**：详细的操作日志和错误信息

### 配置选项
- **VAD超时时间**：语音活动检测超时设置
- **服务端地址**：语音识别API地址
- **用户授权信息**：登录会话标识
- **楼宇空间ID**：业务场景标识
- **其他扩展参数**：支持JSON格式的自定义参数

这个演示页面可以根据实际业务需求进行修改或替换。默认页面加载路径在`strings.xml`中的`h5_url`字符串资源中配置。

## 应用配置管理

应用提供了动态配置功能，支持在不同环境之间快速切换：

### 配置入口

1. **触发方式**：长按屏幕右下角3秒
2. **密码验证**：默认密码为123456
3. **配置界面**：输入密码后弹出配置对话框

### 配置选项

#### 1. H5页面地址配置

- **功能**：设置WebView加载的H5页面地址
- **用途**：在本地assets页面和远程服务器页面之间切换
- **默认值**：`file:///android_asset/voice.html`
- **示例**：`http://*************:6087/daily`

#### 2. 语音服务地址配置

- **功能**：设置语音识别服务端API地址
- **用途**：配置语音请求的目标服务器
- **影响范围**：所有语音识别请求都会发送到此地址
- **示例**：`https://your-api.com/voice`

#### 3. 授权码(Access Key)配置

- **功能**：设置访问服务端的授权密钥
- **用途**：用于生成请求token，确保API访问安全
- **默认值**：`inxvision_panel_2024`
- **安全性**：与服务端预设的access_secret配合生成MD5加密token

### 配置操作

- **保存配置**：点击"保存"按钮，配置立即生效并重新加载页面
- **重置配置**：点击"重置"按钮，恢复所有配置为默认值
- **配置验证**：保存时会验证URL格式的有效性

### 配置存储

- **H5地址**：保存在SharedPreferences中，通过`UrlConfigManager`管理
- **语音服务地址**：保存在`VoiceConfig`中，持久化到SharedPreferences
- **授权码**：保存在`VoiceConfig`中，持久化到SharedPreferences
- **授权密钥**：固定存储在`strings.xml`中，不可通过界面修改
- **自动加载**：应用启动时自动读取最新配置

### 使用场景

#### 开发环境切换

```
开发环境：
- H5地址：http://*************:6087/daily
- 语音服务：http://*************:8080/api/voice

测试环境：
- H5地址：https://test.example.com/voice
- 语音服务：https://test-api.example.com/voice

生产环境：
- H5地址：https://app.example.com/voice
- 语音服务：https://api.example.com/voice
```

#### 本地调试

```
本地开发：
- H5地址：file:///android_asset/voice.html
- 语音服务：http://localhost:8080/api/voice
```

通过长按配置功能，可以快速在不同环境之间切换，无需重新编译应用。

## WebView缓存支持

应用为H5页面提供了完整的WebView缓存支持：

- **DOM Storage**：支持localStorage和sessionStorage
- **应用缓存**：启用AppCache支持
- **数据库存储**：支持WebSQL和IndexedDB
- **HTTP缓存**：遵循HTTP缓存机制
- **地理位置支持**：启用地理位置数据库

H5开发者可以直接使用这些标准Web API进行缓存操作，无需额外的原生开发。

## 功能特色

### 🎙️ 完整语音交互链路

- 热词唤醒 → VAD检测 → 服务端处理 → 结果反馈
- 支持并发请求处理和状态管理
- 完整的错误处理和恢复机制

### 🔇 智能回音消除

- 播放期间自动暂停VAD录音，避免录入系统音频
- 支持热词打断长音频播放
- 统一音频播放入口，确保状态同步

### ⚙️ 灵活配置管理

- 支持H5地址和语音服务地址动态配置
- 长按配置界面，方便环境切换
- 完整的WebView缓存支持

## 未来扩展

该应用设计为易于扩展，可以添加更多原生功能，如：

- 更高级的音频处理算法
- 蓝牙连接和设备管理
- 设备传感器访问
- 相机和图像处理功能
