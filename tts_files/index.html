<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小白马家教管理系统</title>
    <link rel="stylesheet" href="./css/reset.css">
    <link rel="stylesheet" href="./css/common.css">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="./js/config.js"></script>
    <script src="./js/api.js"></script>
    <script src="./js/common.js"></script>
</head>
<body>
    <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">正在加载中...</div>
    </div>

    <script>
        // 检查用户是否已登录，已登录则跳转到需求列表页，否则跳转到登录页
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('token');
            if (token) {
                // 检查token是否有效
                checkToken()
                    .then(result => {
                        if (result.success) {
                            // token有效，跳转到需求列表页
                            window.location.href = './pages/demand.html';
                        } else {
                            // token无效，跳转到登录页
                            window.location.href = './login.html';
                        }
                    })
                    .catch(error => {
                        console.error('Token验证失败:', error);
                        window.location.href = './login.html';
                    });
            } else {
                // 没有token，直接跳转到登录页
                window.location.href = './login.html';
            }
        });

        // 模拟检查token的函数，实际项目中应该调用后端API验证
        function checkToken() {
            return new Promise((resolve) => {
                // 使用localStorage中存储的登录信息，检查是否已过期
                const loginTime = localStorage.getItem('loginTime');
                const current = new Date().getTime();
                
                // 如果登录时间不存在，或者已经超过24小时，则认为token无效
                if (!loginTime || (current - loginTime > 24 * 60 * 60 * 1000)) {
                    localStorage.removeItem('token');
                    localStorage.removeItem('loginTime');
                    resolve({ success: false });
                } else {
                    resolve({ success: true });
                }
            });
        }
    </script>
</body>
</html> 