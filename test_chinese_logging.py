"""
测试中文日志显示
验证Unicode字符是否正确显示
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.logger import get_logger, configure_logging
from src.config import settings

# 配置日志
configure_logging()
logger = get_logger(__name__)


def test_chinese_logging():
    """测试中文日志记录"""
    print("🧪 Testing Chinese Logging")
    print("=" * 40)
    
    # 测试各种中文文本
    test_texts = [
        "你好世界",
        "你能控制哪些设备？",
        "把会议室的灯打开",
        "空调现在是什么状态",
        "语音识别成功完成",
        "阿里云ASR服务正常工作"
    ]
    
    print(f"📋 Current log format: {settings.log_format}")
    print(f"📋 Current log level: {settings.log_level}")
    
    print("\n📝 Testing different log levels with Chinese text:")
    
    for i, text in enumerate(test_texts):
        logger.info(
            f"Test message {i+1}: {text}",
            text_content=text,
            text_length=len(text),
            test_number=i+1
        )
    
    # 测试结构化日志
    logger.info(
        "ASR recognition result",
        result_text="你能控制哪些设备？",
        success=True,
        status=20000000,
        service="ali_asr"
    )
    
    # 测试错误日志
    logger.error(
        "ASR recognition failed",
        error_message="无效的认证令牌",
        error_code="ACCESS_DENIED",
        chinese_error="认证失败"
    )
    
    # 测试警告日志
    logger.warning(
        "Service degradation",
        warning_message="服务降级到模拟模式",
        reason="阿里云ASR服务不可用"
    )
    
    print("\n✅ Chinese logging test completed!")
    print("📋 Check the log output above to verify Chinese characters display correctly")


def test_json_logging():
    """测试JSON格式日志"""
    print("\n🧪 Testing JSON Format Logging")
    print("=" * 40)
    
    # 临时切换到JSON格式
    original_format = settings.log_format
    settings.log_format = "json"
    
    # 重新配置日志
    configure_logging()
    json_logger = get_logger("json_test")
    
    print(f"📋 Switched to JSON format")
    
    # 测试JSON格式的中文日志
    json_logger.info(
        "JSON format test with Chinese",
        chinese_text="你好，这是JSON格式的中文日志测试",
        result="测试成功",
        status="正常"
    )
    
    # 恢复原始格式
    settings.log_format = original_format
    configure_logging()
    
    print("✅ JSON logging test completed!")


def test_unicode_edge_cases():
    """测试Unicode边界情况"""
    print("\n🧪 Testing Unicode Edge Cases")
    print("=" * 40)
    
    edge_cases = [
        "混合English和中文",
        "特殊符号：！@#￥%……&*（）",
        "数字和中文：123个设备",
        "空格 和 制表符\t测试",
        "换行符测试\n第二行",
        "emoji测试：😊🎉✅❌",
        "繁體中文測試",
        "日本語テスト",
        "한국어 테스트"
    ]
    
    for i, text in enumerate(edge_cases):
        logger.info(
            f"Edge case {i+1}",
            test_text=text,
            case_number=i+1
        )
    
    print("✅ Unicode edge cases test completed!")


def main():
    """运行所有测试"""
    print("🧪 Chinese Logging Tests")
    print("=" * 50)
    
    try:
        test_chinese_logging()
        test_json_logging()
        test_unicode_edge_cases()
        
        print("\n🎉 All logging tests completed!")
        print("📋 Summary:")
        print("   - Chinese characters should display correctly")
        print("   - No Unicode escape sequences (\\uXXXX) should appear")
        print("   - Both text and JSON formats should work")
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
