#!/usr/bin/env python3
"""
启动脚本
用于开发环境快速启动服务
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    try:
        from src.main import app
        import uvicorn
        from src.config import settings
        
        print(f"🚀 Starting {settings.app_name} v{settings.app_version}")
        print(f"📍 Server will be available at: http://{settings.host}:{settings.port}")
        print(f"📖 API docs will be available at: http://{settings.host}:{settings.port}/docs")
        print(f"🔍 Health check: http://{settings.host}:{settings.port}/health")
        print("-" * 60)
        
        uvicorn.run(
            app,
            host=settings.host,
            port=6090,  # 使用8001端口避免冲突
            reload=settings.debug,
            log_level=settings.log_level.lower()
        )
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Please install dependencies first: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)
